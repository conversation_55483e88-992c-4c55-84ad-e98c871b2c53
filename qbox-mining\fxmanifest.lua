--[[
    Qbox Mining
    This resource implements a simple yet extensible mining system for the Qbox/QBCore ecosystem.
    It is designed around ox_lib, ox_inventory and optional ox_target/qb-target integration.

    Author: ChatGPT Assistant
    Date: 2025-09-26

    The manifest declares all runtime dependencies and ensures the NUI files are packaged.
--]]

fx_version 'cerulean'
game 'gta5'

lua54 'yes'
use_experimental_fxv2_oal 'yes'

author 'Qbox Mining Team'
description 'Production‑ready mining system for Qbox/QBCore with ox ecosystem'

-- Shared configuration and locale files
shared_scripts {
    '@ox_lib/init.lua',
    'shared/config.lua',
    'shared/locales.lua',
    'locales/*.lua'
}

-- Client side modules
client_scripts {
    'client/cl_main.lua',
    'client/cl_peds.lua',
    'client/cl_target.lua',
    'client/cl_zones.lua',
    'client/cl_animations.lua',
    'client/cl_mining.lua',
    'client/cl_ui.lua'
}

-- Server side modules
server_scripts {
    -- oxmysql is optional; if present the library will be loaded automatically
    '@oxmysql/lib/MySQL.lua',
    'server/sv_main.lua',
    'server/sv_inventory.lua',
    'server/sv_profiles.lua',
    'server/sv_mining.lua',
    'server/sv_smelting.lua',
    'server/sv_selling.lua',
    'server/sv_accounts.lua',
    'server/sv_admin.lua'
}

-- Define the NUI entry point
ui_page 'web/index.html'

-- Include all web files for packaging
files {
    'web/index.html',
    'web/app.js',
    'web/style.css',
    'web/icons/*',
    'web/images/*'
}