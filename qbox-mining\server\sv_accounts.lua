--[[
    server/sv_accounts.lua

    Money handling helpers.  Deposits and withdrawals are performed through Qbox or QBCore
    APIs depending on configuration.  All transactions occur on the server.
--]]

local Accounts = {}

-- Add money to a player's account (cash by default; use accountType = 'bank' for bank)
function Accounts.AddMoney(src, amount, accountType)
    amount = math.floor(amount)
    if amount <= 0 then return end
    if Config.UseQbox and GetResourceState('qbox-core') ~= 'missing' then
        local player = exports['qbox-core']:GetPlayer(src)
        if player and player.AddMoney then
            player:AddMoney(accountType or 'cash', amount)
        end
    else
        local QBCore = exports['qb-core']:GetCoreObject()
        local ply = QBCore.Functions.GetPlayer(src)
        if ply and ply.Functions.AddMoney then
            ply.Functions.AddMoney(accountType or 'cash', amount)
        end
    end
end

-- Remove money from a player's account.  Returns true if successful.
function Accounts.RemoveMoney(src, amount, accountType)
    amount = math.floor(amount)
    if amount <= 0 then return true end
    -- Try to remove from specified account first.  If no accountType is
    -- provided the function will attempt to remove from cash and then bank.
    local accountsToTry = {}
    if accountType then
        accountsToTry[1] = accountType
    else
        accountsToTry = { 'cash', 'bank' }
    end
    for _, acc in ipairs(accountsToTry) do
        if Config.UseQbox and GetResourceState('qbox-core') ~= 'missing' then
            local player = exports['qbox-core']:GetPlayer(src)
            if player and player.RemoveMoney then
                local success = player:RemoveMoney(acc, amount)
                if success then
                    return true
                end
            end
        else
            local QBCore = exports['qb-core']:GetCoreObject()
            local ply = QBCore.Functions.GetPlayer(src)
            if ply and ply.Functions.RemoveMoney then
                local success = ply.Functions.RemoveMoney(acc, amount)
                if success then
                    return true
                end
            end
        end
    end
    return false
end

-- Get player's current balance (cash or bank).  Returns integer or 0 on fail.
function Accounts.GetBalance(src, accountType)
    if Config.UseQbox and GetResourceState('qbox-core') ~= 'missing' then
        local player = exports['qbox-core']:GetPlayer(src)
        if player and player.GetMoney then
            return player:GetMoney(accountType or 'cash') or 0
        end
    else
        local QBCore = exports['qb-core']:GetCoreObject()
        local ply = QBCore.Functions.GetPlayer(src)
        if ply and ply.Functions.GetMoney then
            return ply.Functions.GetMoney(accountType or 'cash') or 0
        end
    end
    return 0
end

return Accounts