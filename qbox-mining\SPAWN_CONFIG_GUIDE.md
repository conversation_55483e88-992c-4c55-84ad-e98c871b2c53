# Rock Spawning Configuration Guide

## Quick Setup

For most servers, these settings in `shared/config.lua` will work well:

```lua
Config.Spawns = {
    maxSpawnedNodes = 75,                    -- Total rocks active at once
    minDistanceBetweenNodes = 8.0,          -- Minimum space between rocks
    useGridBasedSpawning = true,            -- Better distribution
    gridCellSize = 12.0,                    -- Grid spacing
    randomOffsetRange = 4.0,                -- Natural randomness
    maxSpawnAttempts = 500,                 -- Safety limit
    spawnCenter = vector3(2955.7224, 2748.6758, 43.5263),
    spawnRadius = 100.0,
    respawnTime = { min = 45000, max = 60000 },
    playerCooldown = 5000
}
```

## Configuration Options Explained

### Core Settings

- **`maxSpawnedNodes`**: How many rocks spawn simultaneously
  - Lower = less crowded, better performance
  - Higher = more rocks for busy servers
  - Recommended: 50-100

- **`minDistanceBetweenNodes`**: Minimum space between rocks (game units)
  - Prevents overlapping rocks
  - Should be larger than your rock models
  - Recommended: 6.0-10.0

### Distribution Method

- **`useGridBasedSpawning`**: Choose spawn algorithm
  - `true` = Grid-based (recommended) - even coverage
  - `false` = Random - more natural but can cluster

- **`gridCellSize`**: Size of grid squares (when using grid-based)
  - Should be larger than `minDistanceBetweenNodes`
  - Recommended: 10.0-15.0

- **`randomOffsetRange`**: Random variation within grid cells
  - Prevents perfect grid alignment
  - Recommended: 3.0-5.0

### Spawn Area

- **`spawnCenter`**: Center point of the mining area
- **`spawnRadius`**: How far from center rocks can spawn
  - Adjust based on your quarry size
  - Larger radius = more spread out

### Performance

- **`maxSpawnAttempts`**: Safety limit for spawn attempts
  - Prevents infinite loops if settings are impossible
  - Increase if you get "could not spawn" warnings

## Troubleshooting

### "Could only spawn X out of Y nodes"
**Cause**: Settings are too restrictive
**Solutions**:
- Reduce `maxSpawnedNodes`
- Reduce `minDistanceBetweenNodes`
- Increase `spawnRadius`
- Increase `maxSpawnAttempts`

### Rocks spawning in air/underground
**Cause**: Ground detection issues
**Solutions**:
- Adjust `spawnCenter` Z coordinate
- Check if spawn area has valid ground
- Verify coordinates are correct for your map

### Rocks too close together
**Cause**: `minDistanceBetweenNodes` too small
**Solutions**:
- Increase `minDistanceBetweenNodes`
- Use grid-based spawning
- Reduce `maxSpawnedNodes`

### Poor distribution/clustering
**Cause**: Random spawning algorithm
**Solutions**:
- Enable `useGridBasedSpawning = true`
- Adjust `gridCellSize` and `randomOffsetRange`

### Performance issues
**Cause**: Too many rocks
**Solutions**:
- Reduce `maxSpawnedNodes`
- Increase `respawnTime` values
- Check server entity limits

## Server Size Recommendations

### Small Server (1-10 players)
```lua
maxSpawnedNodes = 50
minDistanceBetweenNodes = 10.0
```

### Medium Server (10-30 players)
```lua
maxSpawnedNodes = 75
minDistanceBetweenNodes = 8.0
```

### Large Server (30+ players)
```lua
maxSpawnedNodes = 100
minDistanceBetweenNodes = 6.0
```

## Advanced Configuration

### Custom Spawn Areas
To change the mining location, update:
```lua
spawnCenter = vector3(YOUR_X, YOUR_Y, YOUR_Z)
spawnRadius = YOUR_RADIUS
```

### Rock Model Considerations
If using larger rock models:
- Increase `minDistanceBetweenNodes`
- Increase `gridCellSize`
- May need to reduce `maxSpawnedNodes`

### Performance Optimization
For better server performance:
- Use grid-based spawning
- Keep `maxSpawnedNodes` reasonable
- Monitor entity count
- Consider shorter `respawnTime` with fewer nodes

## Monitoring

Check server console for spawn messages:
- Initialization: `"Spawn area initialized"`
- Progress: `"Spawned X/Y nodes"`
- Warnings: `"Could only spawn X out of Y nodes"`

## Testing Changes

1. Stop the resource
2. Update configuration
3. Start the resource
4. Check console for spawn messages
5. Verify rock distribution in-game

## Common Mistakes

1. **Setting `minDistanceBetweenNodes` larger than `gridCellSize`**
   - Will cause spawn failures
   - Keep `gridCellSize` > `minDistanceBetweenNodes`

2. **Too many nodes for the area**
   - Calculate: Area = π × radius²
   - Node density = maxSpawnedNodes / Area
   - Keep density reasonable

3. **Wrong spawn center coordinates**
   - Verify coordinates match your quarry location
   - Check Z coordinate is near ground level

4. **Ignoring console warnings**
   - Always check logs after configuration changes
   - Warnings indicate configuration issues
