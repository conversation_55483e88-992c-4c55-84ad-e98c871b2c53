--[[
    English localisation for qbox-mining.

    Add any missing keys here.  On client side access translations using the global
    function L(key, ...).  On server side the same function is available.
--]]

return {
    -- General
    ['menu_close'] = 'Close',
    ['menu_back'] = 'Back',
    ['confirm'] = 'Confirm',
    ['cancel'] = 'Cancel',

    -- Peds
    ['shop_ped_label'] = 'Mining Shop',
    ['smelt_ped_label'] = 'Smelting',
    ['sell_ped_label'] = 'Selling',

    -- Shop Tabs
    ['tab_pickaxes'] = 'Pickaxes',
    ['tab_upgrades'] = 'Upgrades',
    ['tab_repair'] = 'Repair',
    ['tab_stats'] = 'My Stats',
    ['tab_help'] = 'Help',

    -- Smelt Tabs
    ['tab_recipes'] = 'Recipes',
    ['tab_queue'] = 'Queue',
    ['tab_history'] = 'History',

    -- Sell Tabs
    ['tab_sell'] = 'Sell',
    ['tab_bonuses'] = 'Bonuses',
    ['tab_rates'] = 'Rates',
    ['tab_contracts'] = 'Contracts',

    -- Shop Text
    ['pickaxe_purchase'] = 'Purchase Pickaxe',
    ['pickaxe_upgrade'] = 'Upgrade Pickaxe',
    ['pickaxe_repair'] = 'Repair Pickaxe',
    ['your_level'] = 'Level %s',
    ['xp_to_next'] = '%s XP to next level',
    ['session_stats'] = 'Session Stats',
    ['nothing_to_display'] = 'Nothing to display.',

    -- Mining
    ['mining_progress'] = 'Mining %s...',
    ['mining_no_pickaxe'] = 'You need a pickaxe to mine.',
    ['mining_too_far'] = 'That node is too far away.',
    ['mining_on_cooldown'] = 'You need to wait before mining again.',
    ['mining_success'] = 'You mined %sx %s.',
    ['mining_rare'] = 'You found a rare item: %s!',

    -- Smelting
    ['smelt_not_enough_ore'] = 'You do not have enough %s.',
    ['smelt_in_progress'] = 'Smelting %s...',
    ['smelt_complete'] = 'Smelting complete! You received %sx %s.',
    ['smelt_cancelled'] = 'Smelting cancelled.',

    -- Selling
    ['sell_no_items'] = 'You have nothing to sell.',
    ['sell_summary'] = 'Selling %s items for $%s (tax %s).',
    ['sell_success'] = 'You sold your items for $%s.',

    -- Upgrades
    ['upgrade_speed'] = 'Speed',
    ['upgrade_yield'] = 'Yield',
    ['upgrade_luck'] = 'Luck',
    ['upgrade_level'] = 'Level %s/%s',
    ['upgrade_cost'] = 'Cost: $%s',
    ['upgrade_no_pickaxe'] = 'You don\'t own this pickaxe.',
    ['upgrade_maxed'] = 'This upgrade is maxed out.',
    ['upgrade_success'] = 'Upgrade successful!',

    -- Repair
    ['repair_select'] = 'Select a pickaxe to repair.',
    ['repair_cost'] = 'Repair cost: $%s',
    ['repair_success'] = 'Your pickaxe has been repaired.',

    -- Contracts
    ['contract_inactive'] = 'No contract available.',
    ['contract_progress'] = 'Progress: %s/%s %s',
    ['contract_complete'] = 'Contract completed! Reward: $%s and %s XP.',

    -- Admin
    ['admin_no_permission'] = 'You do not have permission.',
    ['admin_setlevel'] = 'Set player %s mining level to %s.',
    ['admin_addxp'] = 'Added %s XP to player %s.',
    ['admin_reset'] = 'Reset mining data for player %s.',
    ['admin_debug_on'] = 'Mining debug enabled.',
    ['admin_debug_off'] = 'Mining debug disabled.'
}