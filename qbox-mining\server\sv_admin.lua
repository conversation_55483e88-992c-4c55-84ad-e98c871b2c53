--[[
    server/sv_admin.lua

    Provides administrator commands for managing player mining data.  Access can be
    restricted by ACE permissions or Qbox group checks.  See Config section for
    command names and modify as needed.
--]]

local Profiles = require('server.sv_profiles')

-- Simple permission check using ACE.  Adjust this to integrate with your admin system.
local function hasPerm(src)
    return IsPlayerAceAllowed(src, 'qbox-mining.admin')
end

-- Set a player's mining level
RegisterCommand('mining:setlevel', function(source, args)
    if not hasPerm(source) then
        TriggerClientEvent('ox_lib:notify', source, { description = L('admin_no_permission'), type = 'error' })
        return
    end
    local target = tonumber(args[1] or '0')
    local level = tonumber(args[2] or '0')
    if target <= 0 or level <= 0 then return end
    Profiles.SetLevel(target, level)
    TriggerClientEvent('ox_lib:notify', source, { description = L('admin_setlevel', target, level), type = 'inform' })
end)

-- Add XP to a player
RegisterCommand('mining:addxp', function(source, args)
    if not hasPerm(source) then
        TriggerClientEvent('ox_lib:notify', source, { description = L('admin_no_permission'), type = 'error' })
        return
    end
    local target = tonumber(args[1] or '0')
    local amount = tonumber(args[2] or '0')
    if target <= 0 or amount <= 0 then return end
    Profiles.AddXpRaw(target, amount)
    TriggerClientEvent('ox_lib:notify', source, { description = L('admin_addxp', amount, target), type = 'inform' })
end)

-- Reset a player's mining data
RegisterCommand('mining:reset', function(source, args)
    if not hasPerm(source) then
        TriggerClientEvent('ox_lib:notify', source, { description = L('admin_no_permission'), type = 'error' })
        return
    end
    local target = tonumber(args[1] or '0')
    if target <= 0 then return end
    Profiles.Reset(target)
    TriggerClientEvent('ox_lib:notify', source, { description = L('admin_reset', target), type = 'inform' })
end)

-- Debug toggle (no implementation here; left for expansion)
RegisterCommand('mining:debug', function(source, args)
    if not hasPerm(source) then return end
    TriggerClientEvent('qbox-mining:client:toggleDebug', source)
    -- Feedback message toggled on client
end)