--[[
    server/sv_profiles.lua

    Maintains player mining profiles (level and XP).  Profiles are stored in memory and
    optionally persisted to player metadata.  Functions are provided to get the profile,
    add XP, set level and reset progress.  When a player levels up the client is
    notified so the UI can update.
--]]

local Profiles = {}

-- Helper to get the Qbox/QBCore player object
local function GetPlayer(src)
    if Config.UseQbox and GetResourceState('qbox-core') ~= 'missing' then
        return exports['qbox-core']:GetPlayer(src)
    else
        local QBCore = exports['qb-core']:GetCoreObject()
        return QBCore.Functions.GetPlayer(src)
    end
end

-- Load or create a profile for a player
function Profiles.GetProfile(src)
    local prof = Profiles[src]
    if prof then return prof end
    -- Attempt to load from player metadata if available
    local ply = GetPlayer(src)
    if ply and ply.PlayerData and ply.PlayerData.metadata and ply.PlayerData.metadata.mining then
        prof = ply.PlayerData.metadata.mining
    else
        prof = { level = 1, xp = 0 }
    end
    Profiles[src] = prof
    return prof
end

-- Save profile back to player metadata
local function saveProfile(src)
    local ply = GetPlayer(src)
    if not ply then return end
    local prof = Profiles[src]
    if Config.UseQbox and ply.SetMetaData then
        ply:SetMetaData('mining', prof)
    elseif ply.Functions then
        ply.Functions.SetMetaData('mining', prof)
    end
end

-- Add XP and handle level ups
function Profiles.AddXp(src, amount)
    local prof = Profiles.GetProfile(src)
    prof.xp = (prof.xp or 0) + amount
    while prof.level < Config.MaxLevel and prof.xp >= Config.levelXp(prof.level) do
        prof.xp = prof.xp - Config.levelXp(prof.level)
        prof.level = prof.level + 1
        -- Could grant rewards or notify player here
    end
    saveProfile(src)
    TriggerClientEvent('qbox-mining:client:updateXP', src, prof.level, prof.xp)
end

-- Set level directly (admin)
function Profiles.SetLevel(src, level)
    local prof = Profiles.GetProfile(src)
    prof.level = math.min(level, Config.MaxLevel)
    prof.xp = 0
    saveProfile(src)
    TriggerClientEvent('qbox-mining:client:updateXP', src, prof.level, prof.xp)
end

-- Add XP without applying level logic (admin)
function Profiles.AddXpRaw(src, amount)
    local prof = Profiles.GetProfile(src)
    prof.xp = prof.xp + amount
    saveProfile(src)
    TriggerClientEvent('qbox-mining:client:updateXP', src, prof.level, prof.xp)
end

-- Reset profile (admin)
function Profiles.Reset(src)
    Profiles[src] = { level = 1, xp = 0 }
    saveProfile(src)
    TriggerClientEvent('qbox-mining:client:updateXP', src, 1, 0)
end

return Profiles