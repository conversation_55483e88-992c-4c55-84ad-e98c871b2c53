--[[
    client/cl_zones.lua

    Defines mining zones using ox_lib.  When a player enters a zone the server is
    notified.  When a player leaves all zone names it is also notified.  This can be
    used to spawn/despawn nodes or apply bonuses based on nearby players.
--]]

local zones = {}
local currentZone = nil

function SetupMiningZones()
    for zoneName, def in pairs(Config.Zones) do
        if def.type == 'poly' and def.points then
            -- Convert 2D vectors to 3D vectors.  ox_lib polyzones expect
            -- either a vector3 or a table with x/y/z fields.  Older configs
            -- may define polygon points as vec2; convert them here to
            -- vector3 with a zero z‑coordinate.  This prevents the
            -- "expected type 'vector3' or 'table' (received vector2)"
            -- error when creating zones.
            local points = {}
            for i = 1, #def.points do
                local p = def.points[i]
                if type(p) == 'vector2' then
                    points[i] = vector3(p.x, p.y, 0.0)
                elseif type(p) == 'vector3' then
                    points[i] = p
                elseif type(p) == 'table' and p.x and p.y then
                    -- allow table definitions with optional z
                    points[i] = vector3(p.x, p.y, p.z or 0.0)
                else
                    -- fallback: try to construct vector3
                    points[i] = vector3(p[1], p[2], p[3] or 0.0)
                end
            end
            zones[zoneName] = lib.zones.poly({
                points = points,
                thickness = def.thickness or 20.0,
                debug = false,
                inside = function()
                    if currentZone ~= zoneName then
                        currentZone = zoneName
                        TriggerServerEvent('qbox-mining:server:enterZone', zoneName)
                    end
                end,
                onExit = function()
                    if currentZone == zoneName then
                        currentZone = nil
                        TriggerServerEvent('qbox-mining:server:leaveZone', zoneName)
                    end
                end
            })
        elseif def.type == 'circle' and def.coords and def.radius then
            zones[zoneName] = lib.zones.sphere({
                coords = def.coords,
                radius = def.radius,
                debug = false,
                inside = function()
                    if currentZone ~= zoneName then
                        currentZone = zoneName
                        TriggerServerEvent('qbox-mining:server:enterZone', zoneName)
                    end
                end,
                onExit = function()
                    if currentZone == zoneName then
                        currentZone = nil
                        TriggerServerEvent('qbox-mining:server:leaveZone', zoneName)
                    end
                end
            })
        end
    end
end

function GetCurrentZone()
    return currentZone
end

exports('SetupMiningZones', SetupMiningZones)
exports('GetCurrentZone', GetCurrentZone)