--[[
    server/sv_main.lua

    Core server logic for qbox-mining.  Handles node spawning and respawning,
    player profile management, and server callbacks for mining, smelting and selling.
    Many functions delegate to submodules (inventory, profiles, accounts) to keep
    responsibilities separated.
--]]

local Nodes = {}
local NextNodeId = 1
local PlayerLastMine = {}

--[[
    SpawnArea stores precomputed boundaries of the quarry defined via
    Config.Spawns.spawnBounds.  These values (minX, maxX, etc.) and the
    derived centre and maximum radius are calculated once at resource start
    by ComputeSpawnArea().  Having these numbers ready avoids expensive
    recalculation every time a rock spawns or respawns.
]]
local SpawnArea = {
    minX = nil,
    maxX = nil,
    minY = nil,
    maxY = nil,
    minZ = nil,
    maxZ = nil,
    centerX = nil,
    centerY = nil,
    maxRadius = nil
}

--[[
    Spawn configuration is now loaded from Config.Spawns to maintain consistency
    and allow easier configuration changes. The spawn center and radius are
    configurable through the shared config file.
]]
local SpawnCenter = nil
local SpawnRadius = nil

--[[
    pointInPolygon(polygon, x, y)

    Determine whether a given 2D point (x, y) lies inside a polygon.  This uses the
    classic ray‑casting algorithm described in computational geometry, where a
    horizontal ray extending to the right from the test point is cast and the number
    of times it crosses polygon edges is counted.  If the count is odd the point is
    inside; if even it is outside【52545366621268†L170-L183】.  The polygon should
    be defined as a table of vertices, each vertex being either a vec2/vector2 or a
    two‑element table { x, y }.  The function treats missing or malformed
    vertices as empty and will simply skip them.
--]]
local function pointInPolygon(polygon, x, y)
    local inside = false
    local j = #polygon
    for i = 1, #polygon do
        local vi = polygon[i]
        local vj = polygon[j]
        local xi = (vi.x ~= nil) and vi.x or vi[1]
        local yi = (vi.y ~= nil) and vi.y or vi[2]
        local xj = (vj.x ~= nil) and vj.x or vj[1]
        local yj = (vj.y ~= nil) and vj.y or vj[2]
        if xi and yi and xj and yj then
            -- Check if the point is between the y‑coords of the edge
            local intersect = ((yi > y) ~= (yj > y)) and (x < ((xj - xi) * (y - yi) / (yj - yi) + xi))
            if intersect then
                inside = not inside
            end
        end
        j = i
    end
    return inside
end

--[[
    findGroundZ(x, y, defaultZ)

    Attempts to determine the ground height at the given X/Y coordinates using the
    native GetGroundZFor_3dCoord.  This function will return the found Z value if
    successful, or fall back to the provided defaultZ (or SpawnArea.minZ) when the
    call fails.  Using GetGroundZFor_3dCoord to align entities with the world
    surface is a common pattern when spawning objects in FiveM【690177295845236†L640-L647】.
--]]
local function findGroundZ(x, y, defaultZ)
    local baseZ = defaultZ or (SpawnCenter and SpawnCenter.z or 43.0)

    -- Try multiple starting heights to improve success rate
    local startHeights = { baseZ + 50.0, baseZ + 100.0, baseZ + 20.0, baseZ }

    for _, startZ in ipairs(startHeights) do
        local ok, groundZ = GetGroundZFor_3dCoord(x + 0.0, y + 0.0, startZ + 0.0)
        if ok and groundZ then
            -- Validate that the ground height is reasonable (not too far from expected)
            local heightDiff = math.abs(groundZ - baseZ)
            if heightDiff < 50.0 then -- Allow up to 50 units difference
                return groundZ
            end
        end
    end

    -- If all attempts fail, use the base Z coordinate
    return baseZ
end

-- Forward declarations of helper modules
local Inventory = {}
local Profiles = {}
local Accounts = {}

-- Utility: choose an ore type based on rarity weights
local function RandomOreType()
    local total = 0
    for _, v in pairs(Config.Ores) do total = total + v.rarity end
    local roll = math.random(total)
    local cumulative = 0
    for oreId, v in pairs(Config.Ores) do
        cumulative = cumulative + v.rarity
        if roll <= cumulative then return oreId end
    end
    return 'iron'
end

--[[
    GetOreByPosition(pos)

    Given a vector3 position, returns the ore ID appropriate for that point
    within the spawn area.  The logic divides the spawn area into
    concentric rings: the farther from the centre, the more common (and less
    valuable) the ore.  The ring thresholds are percentages of the
    maximum radius recorded in SpawnArea.  If SpawnArea hasn't been
    initialised (e.g. spawnBounds missing), this falls back to a random
    selection to ensure nodes still spawn.
]]
local function GetOreByPosition(pos)
    if not SpawnArea.maxRadius or SpawnArea.maxRadius == 0 then
        -- fallback to random selection if spawn area hasn't been initialised yet
        local keys = {}
        for oreId in pairs(Config.Ores) do keys[#keys + 1] = oreId end
        return keys[math.random(#keys)]
    end
    -- compute 2D distance from the centre of the spawn area
    local dx = pos.x - SpawnArea.centerX
    local dy = pos.y - SpawnArea.centerY
    local dist = math.sqrt(dx * dx + dy * dy)
    local ratio = dist / SpawnArea.maxRadius
    -- Outermost ring (>= 75% of radius): spawn the most common ore
    if ratio >= 0.75 then return 'iron' end
    -- Middle ring (50–75%): copper
    if ratio >= 0.50 then return 'copper' end
    -- Inner-middle ring (30–50%): aluminum
    if ratio >= 0.30 then return 'aluminum' end
    -- Near centre (10–30%): gold
    if ratio >= 0.10 then return 'gold' end
    -- Very centre (< 10%): highest tier ore
    return 'titanium'
end

--[[
    ComputeSpawnArea()

    Initializes the spawn area using configuration values from Config.Spawns.
    Sets up the SpawnCenter and SpawnRadius from config, then calculates the
    spawn area boundaries. This creates a circular spawning area centered on
    the quarry with a configurable radius.
]]
local function ComputeSpawnArea()
    -- Load spawn configuration from Config.Spawns
    SpawnCenter = Config.Spawns.spawnCenter or vector3(2955.7224, 2748.6758, 43.5263)
    SpawnRadius = Config.Spawns.spawnRadius or 100.0

    -- Set SpawnArea based on the configured center and radius
    local cx, cy, cz = SpawnCenter.x, SpawnCenter.y, SpawnCenter.z
    SpawnArea.minX = cx - SpawnRadius
    SpawnArea.maxX = cx + SpawnRadius
    SpawnArea.minY = cy - SpawnRadius
    SpawnArea.maxY = cy + SpawnRadius
    -- Provide a small vertical range around the centre.  The client will use
    -- PlaceObjectOnGroundProperly to ensure rocks sit on the ground.
    SpawnArea.minZ = cz - 10.0
    SpawnArea.maxZ = cz + 10.0
    SpawnArea.centerX = cx
    SpawnArea.centerY = cy
    SpawnArea.maxRadius = SpawnRadius

    print(string.format("[qbox-mining] Spawn area initialized: Center(%.2f, %.2f, %.2f), Radius: %.2f",
        cx, cy, cz, SpawnRadius))
end

--[[
    CheckPositionCollision(pos, minDistance)

    Checks if a position is too close to any existing active nodes.
    Returns true if there's a collision (position is too close), false if clear.
]]
local function CheckPositionCollision(pos, minDistance)
    minDistance = minDistance or (Config.Spawns.minDistanceBetweenNodes or 8.0)

    for _, node in pairs(Nodes) do
        if node.active and node.coords then
            local dx = pos.x - node.coords.x
            local dy = pos.y - node.coords.y
            local distance = math.sqrt(dx * dx + dy * dy)

            if distance < minDistance then
                return true -- Collision detected
            end
        end
    end

    return false -- No collision
end

--[[
    IsValidSpawnPosition(pos)

    Validates if a position is suitable for spawning a rock.
    Checks ground validity and ensures the position is within the spawn area.
]]
local function IsValidSpawnPosition(pos)
    -- Check if position is within spawn radius
    if SpawnCenter then
        local dx = pos.x - SpawnCenter.x
        local dy = pos.y - SpawnCenter.y
        local distance = math.sqrt(dx * dx + dy * dy)

        if distance > SpawnRadius then
            return false
        end
    end

    -- Additional ground validation could be added here
    -- For now, we rely on findGroundZ to provide valid positions

    return true
end

-- Spawn a single node at a position
local function SpawnNode(pos)
    local id = NextNodeId
    NextNodeId = NextNodeId + 1
    -- determine ore based on position relative to spawn area
    local ore = GetOreByPosition(pos)
    local oreDef = Config.Ores[ore] or {}
    local model = oreDef.model or 'prop_rock_4_g'
    Nodes[id] = {
        id = id,
        coords = pos,
        ore = ore,
        active = true,
        respawnAt = 0,
        model = model
    }
end

--[[
    GenerateGridBasedPositions(maxNodes)

    Generates spawn positions using a grid-based approach for better distribution.
    This ensures more even coverage of the spawn area compared to pure random.
]]
local function GenerateGridBasedPositions(maxNodes)
    local positions = {}
    local cellSize = Config.Spawns.gridCellSize or 12.0
    local offsetRange = Config.Spawns.randomOffsetRange or 4.0

    -- Calculate grid dimensions
    local gridRadius = math.ceil(SpawnRadius / cellSize)
    local centerGridX = 0
    local centerGridY = 0

    -- Generate grid positions in a spiral pattern for better distribution
    local gridPositions = {}

    -- Start from center and spiral outward
    for radius = 0, gridRadius do
        if radius == 0 then
            table.insert(gridPositions, {x = centerGridX, y = centerGridY})
        else
            -- Generate positions in a ring at this radius
            local circumference = 8 * radius -- Approximate circumference for grid
            for i = 1, circumference do
                local angle = (i / circumference) * 2 * math.pi
                local gridX = centerGridX + math.floor(radius * math.cos(angle) + 0.5)
                local gridY = centerGridY + math.floor(radius * math.sin(angle) + 0.5)

                -- Check if this grid position is unique
                local unique = true
                for _, existing in ipairs(gridPositions) do
                    if existing.x == gridX and existing.y == gridY then
                        unique = false
                        break
                    end
                end

                if unique then
                    table.insert(gridPositions, {x = gridX, y = gridY})
                end
            end
        end
    end

    -- Convert grid positions to world positions
    for i = 1, math.min(#gridPositions, maxNodes) do
        local gridPos = gridPositions[i]

        -- Calculate base world position
        local baseX = SpawnCenter.x + (gridPos.x * cellSize)
        local baseY = SpawnCenter.y + (gridPos.y * cellSize)

        -- Add random offset within the cell
        local offsetX = (math.random() - 0.5) * offsetRange
        local offsetY = (math.random() - 0.5) * offsetRange

        local worldX = baseX + offsetX
        local worldY = baseY + offsetY

        -- Check if position is within spawn radius
        local dx = worldX - SpawnCenter.x
        local dy = worldY - SpawnCenter.y
        local distance = math.sqrt(dx * dx + dy * dy)

        if distance <= SpawnRadius then
            local z = findGroundZ(worldX, worldY, SpawnCenter.z)
            table.insert(positions, vector3(worldX, worldY, z))
        end
    end

    return positions
end

-- Spawn initial nodes based on config with collision detection
local function SpawnInitialNodes()
    if not SpawnCenter or not SpawnRadius then
        print("[qbox-mining] ERROR: Spawn area not initialized!")
        return
    end

    local maxNodes = Config.Spawns.maxSpawnedNodes or 75
    local maxAttempts = Config.Spawns.maxSpawnAttempts or 500
    local minDistance = Config.Spawns.minDistanceBetweenNodes or 8.0
    local useGridBased = Config.Spawns.useGridBasedSpawning or false
    local spawnedCount = 0
    local attemptCount = 0

    print(string.format("[qbox-mining] Starting spawn process: Target=%d nodes, MinDistance=%.1f, Method=%s",
        maxNodes, minDistance, useGridBased and "Grid-based" or "Random"))

    if useGridBased then
        -- Use grid-based distribution
        local candidatePositions = GenerateGridBasedPositions(maxNodes * 2) -- Generate more candidates than needed

        for _, pos in ipairs(candidatePositions) do
            if spawnedCount >= maxNodes then break end
            attemptCount = attemptCount + 1

            if IsValidSpawnPosition(pos) and not CheckPositionCollision(pos, minDistance) then
                SpawnNode(pos)
                spawnedCount = spawnedCount + 1

                if spawnedCount % 10 == 0 then
                    print(string.format("[qbox-mining] Spawned %d/%d nodes", spawnedCount, maxNodes))
                end
            end
        end
    else
        -- Use random distribution (original method)
        while spawnedCount < maxNodes and attemptCount < maxAttempts do
            attemptCount = attemptCount + 1

            -- Generate a random position using polar coordinates for even distribution
            local angle = math.random() * (2.0 * math.pi)
            local radius = SpawnRadius * math.sqrt(math.random())
            local x = SpawnCenter.x + radius * math.cos(angle)
            local y = SpawnCenter.y + radius * math.sin(angle)

            -- Find ground height for this position
            local z = findGroundZ(x, y, SpawnCenter.z)
            local pos = vector3(x, y, z)

            -- Validate the position
            if IsValidSpawnPosition(pos) and not CheckPositionCollision(pos, minDistance) then
                SpawnNode(pos)
                spawnedCount = spawnedCount + 1

                -- Log progress every 10 spawns
                if spawnedCount % 10 == 0 then
                    print(string.format("[qbox-mining] Spawned %d/%d nodes (attempts: %d)",
                        spawnedCount, maxNodes, attemptCount))
                end
            end
        end
    end

    print(string.format("[qbox-mining] Spawn complete: %d/%d nodes spawned in %d attempts",
        spawnedCount, maxNodes, attemptCount))

    if spawnedCount < maxNodes then
        print(string.format("[qbox-mining] WARNING: Could only spawn %d out of %d requested nodes. Consider reducing maxSpawnedNodes or minDistanceBetweenNodes.",
            spawnedCount, maxNodes))
    end

    BroadcastNodes()
end

-- Respawn nodes when their timer expires
local function TickRespawn()
    local now = GetGameTimer()
    local changed = false
    for id, node in pairs(Nodes) do
        if not node.active and now >= node.respawnAt then
            node.active = true
            -- reassign ore based on current position; this keeps the ring
            -- distribution when nodes respawn instead of pulling a random tier
            node.ore = GetOreByPosition(node.coords)
            node.respawnAt = 0
            changed = true
        end
    end
    if changed then
        BroadcastNodes()
    end
end

-- Broadcast active nodes to clients
function BroadcastNodes(playerId)
    local payload = {}
    for id, node in pairs(Nodes) do
        if node.active then
            payload[id] = { coords = node.coords, ore = node.ore, model = node.model }
        end
    end
    if playerId then
        TriggerClientEvent('qbox-mining:client:updateNodes', playerId, payload)
    else
        TriggerClientEvent('qbox-mining:client:updateNodes', -1, payload)
    end
end

-- Initialize helper modules.  These are separated into their own files for clarity.
AddEventHandler('onResourceStart', function(res)
    if res ~= GetCurrentResourceName() then return end
    Inventory = require('server.sv_inventory')
    Profiles  = require('server.sv_profiles')
    Accounts  = require('server.sv_accounts')
    -- Spawn nodes shortly after start
    CreateThread(function()
        Wait(2000)
        -- Compute the spawn area boundaries based off Config.Spawns.spawnBounds
        ComputeSpawnArea()
        SpawnInitialNodes()
    end)
    -- Respawn tick
    CreateThread(function()
        while true do
            TickRespawn()
            -- check more frequently to support faster respawn times (approx every 5 seconds)
            Wait(5000)
        end
    end)
end)

-- When player requests their profile send it
RegisterNetEvent('qbox-mining:server:requestProfile', function()
    local src = source
    local profile = Profiles.GetProfile(src)
    TriggerClientEvent('qbox-mining:client:setProfile', src, profile)
    -- Send node data
    BroadcastNodes(src)
end)

-- Player enters zone (optional hook)
RegisterNetEvent('qbox-mining:server:enterZone', function(zoneName)
    -- Could spawn or modify nodes based on zone if necessary
end)

RegisterNetEvent('qbox-mining:server:leaveZone', function(zoneName)
end)

----------------------------------------------------------------------------------------------------
-- Mining callbacks
--
-- Validates whether a player can start mining a given node and returns timing modifiers
lib.callback.register('qbox-mining:server:startMining', function(src, nodeId)
    local node = Nodes[nodeId]
    if not node or not node.active then
        return { success = false, message = L('mining_too_far') }
    end
    -- Enforce server side player cooldown
    local now = os.time() * 1000
    local last = PlayerLastMine[src] or 0
    if now - last < Config.Spawns.playerCooldown then
        return { success = false, message = L('mining_on_cooldown') }
    end
    -- Check zone (client ensures but double check server side)
    -- TODO: verify player is within Config.Zones; omitted for brevity
    -- Check pickaxe in inventory
    local pickaxeSlot, pickaxeItem, metadata = Inventory.GetBestPickaxe(src)
    if not pickaxeSlot then
        return { success = false, message = L('mining_no_pickaxe') }
    end
    -- Determine mining duration and speed modifier
    local baseDuration = 6000 -- base time in ms; could be adjusted per ore tier
    local pickaxeDef = Config.Pickaxes[pickaxeItem.name]
    local speed = pickaxeDef and pickaxeDef.speed or 1.0
    local upgrades = metadata.upgrades or {}
    local upgradeSpeedLevel = upgrades.speed or 0
    local speedModifier = math.max(0.5, speed * (1 - 0.05 * upgradeSpeedLevel))
    -- Mark last mine
    PlayerLastMine[src] = now
    return { success = true, duration = baseDuration, speed = speedModifier }
end)

-- When mining finishes successfully award items and XP
RegisterNetEvent('qbox-mining:server:finishMining', function(nodeId)
    local src = source
    local player = src
    local node = Nodes[nodeId]
    if not node or not node.active then return end
    -- Validate again: ensure player has pickaxe and remove durability
    local slot, itemData, metadata = Inventory.GetBestPickaxe(src)
    if not slot then return end
    -- Reduce durability
    local loss = Config.DurabilityLoss[itemData.name] or 1
    metadata.durability = (metadata.durability or 100) - loss
    if metadata.durability < 0 then metadata.durability = 0 end
    -- Save metadata back to inventory
    Inventory.UpdateItemMetadata(src, slot, metadata)
    -- Determine yield
    local oreDef = Config.Ores[node.ore]
    local amount = math.random(oreDef.yield.min, oreDef.yield.max)
    -- Yield upgrade
    local upgrades = metadata.upgrades or {}
    local yieldLevel = upgrades.yield or 0
    if yieldLevel > 0 then
        -- chance to add one extra per level (5% each) for each level
        if math.random(100) <= yieldLevel * 5 then
            amount = amount + 1
        end
    end
    -- Give ore to player
    Inventory.GiveItem(src, oreDef.item, amount)
    -- Rare drop roll
    local rareChance = Config.RareDrops.chance or 0
    local luckLevel = upgrades.luck or 0
    local effectiveChance = rareChance + (luckLevel * 2) -- +2% per luck level
    if math.random(100) <= effectiveChance then
        local drop = Config.RareDrops.drops[math.random(#Config.RareDrops.drops)]
        local qty = math.random(drop.min, drop.max)
        Inventory.GiveItem(src, drop.item, qty)
        TriggerClientEvent('ox_lib:notify', src, { description = L('mining_rare', drop.item), type = 'inform' })
    end
    -- Award XP
    Profiles.AddXp(src, oreDef.xp)
    -- Deactivate node and set respawn timer
    node.active = false
    local minTime = Config.Spawns.respawnTime.min
    local maxTime = Config.Spawns.respawnTime.max
    node.respawnAt = GetGameTimer() + math.random(minTime, maxTime)
    BroadcastNodes()
end)

-- Mining was cancelled; reset cooldown but do not award anything
RegisterNetEvent('qbox-mining:server:cancelMining', function(nodeId)
    -- Mining was cancelled by the player.  Reset the player's last mine time so they
    -- aren't unfairly locked out by the server‑side cooldown.  Without this the
    -- server would treat the aborted attempt as a successful attempt and enforce
    -- the Config.Spawns.playerCooldown.  Resetting here allows players to retry
    -- immediately after cancelling.
    local src = source
    PlayerLastMine[src] = 0
end)

----------------------------------------------------------------------------------------------------
-- Shop callbacks: purchase, upgrade, repair

lib.callback.register('qbox-mining:server:purchasePickaxe', function(src, pickaxeName)
    local def = Config.Pickaxes[pickaxeName]
    if not def then return false, 'Invalid pickaxe' end
    -- Check if player already owns this pickaxe; allow multiple
    local cost = def.price
    if not Accounts.RemoveMoney(src, cost, 'cash') then
        return false, 'Insufficient funds'
    end
    -- Build metadata for pickaxe
    local metadata = {
        durability = def.durability,
        maxDurability = def.durability,
        upgrades = { speed = 0, yield = 0, luck = 0 }
    }
    Inventory.GiveItem(src, pickaxeName, 1, metadata)
    return true, 'Purchased successfully'
end)

lib.callback.register('qbox-mining:server:upgradePickaxe', function(src, pickaxeName, upgrade)
    local def = Config.Pickaxes[pickaxeName]
    local upDef = Config.Upgrades[upgrade]
    if not def or not upDef then return false, 'Invalid upgrade' end
    -- Find the pickaxe item
    local slot, itemData, metadata = Inventory.GetBestPickaxe(src)
    if not slot or itemData.name ~= pickaxeName then
        return false, L('upgrade_no_pickaxe')
    end
    metadata.upgrades = metadata.upgrades or { speed = 0, yield = 0, luck = 0 }
    local currentLevel = metadata.upgrades[upgrade] or 0
    if currentLevel >= upDef.max then
        return false, L('upgrade_maxed')
    end
    -- Calculate cost
    local price = math.floor(upDef.priceBase * (upDef.priceScale ^ currentLevel))
    if not Accounts.RemoveMoney(src, price, 'cash') then
        return false, 'Insufficient funds'
    end
    -- Apply upgrade
    metadata.upgrades[upgrade] = currentLevel + 1
    Inventory.UpdateItemMetadata(src, slot, metadata)
    return true, L('upgrade_success')
end)

lib.callback.register('qbox-mining:server:repairPickaxe', function(src, pickaxeName)
    local def = Config.Pickaxes[pickaxeName]
    if not def then return false, 'Invalid pickaxe' end
    local slot, itemData, metadata = Inventory.GetBestPickaxe(src)
    if not slot or itemData.name ~= pickaxeName then return false, L('upgrade_no_pickaxe') end
    local cur = metadata.durability or def.durability
    local max = metadata.maxDurability or def.durability
    if cur >= max then return false, 'Pickaxe is already at full durability' end
    local missing = max - cur
    -- Cost scales with missing durability and base price
    local price = math.floor((missing / max) * def.price * 0.5)
    if not Accounts.RemoveMoney(src, price, 'cash') then
        return false, 'Insufficient funds'
    end
    metadata.durability = max
    Inventory.UpdateItemMetadata(src, slot, metadata)
    return true, L('repair_success')
end)

----------------------------------------------------------------------------------------------------
-- Smelting callback
lib.callback.register('qbox-mining:server:startSmelting', function(src, recipeIndex, quantity)
    local recipe = Config.Smelting[recipeIndex]
    if not recipe then return false, 'Invalid recipe' end
    quantity = tonumber(quantity) or 1
    local required = recipe.inputQty * quantity
    -- Check player has enough ore
    local has = Inventory.CountItem(src, recipe.input)
    if has < required then
        return false, L('smelt_not_enough_ore', recipe.input)
    end
    -- Remove items
    Inventory.RemoveItem(src, recipe.input, required)
    -- Create smelting task (simplified: immediate)
    local outputQty = quantity
    Inventory.GiveItem(src, recipe.output, outputQty)
    Profiles.AddXp(src, recipe.xp * quantity)
    return true, L('smelt_complete', outputQty, recipe.output)
end)

----------------------------------------------------------------------------------------------------
-- Selling callback
lib.callback.register('qbox-mining:server:sellItems', function(src, items)
    -- items is a list of { item = itemName, qty = amount }
    local total = 0
    for _, entry in ipairs(items) do
        local qty = entry.qty
        -- qty of -1 means sell all of that item
        if qty == -1 then
            qty = Inventory.CountItem(src, entry.item)
        end
        if qty > 0 then
            -- find price definition
            local priceDef
            for _, p in ipairs(Config.SellPrices) do
                if p.item == entry.item then priceDef = p break end
            end
            if priceDef then
                local base = priceDef.base
                local variance = priceDef.variance or 0
                local multiplier = 1.0 + (math.random(-variance*100, variance*100)/100)
                total = total + (base * qty * multiplier)
                -- remove items now
                Inventory.RemoveItem(src, entry.item, qty)
            end
        end
    end
    if total <= 0 then
        return false, L('sell_no_items')
    end
    -- Apply tax
    local tax = Config.TaxRate or 0
    local taxAmount = total * tax
    local payout = math.floor(total - taxAmount)
    -- Pay player
    Accounts.AddMoney(src, payout)
    Profiles.AddXp(src, math.floor(payout / 100))
    return true, L('sell_success', payout)
end)