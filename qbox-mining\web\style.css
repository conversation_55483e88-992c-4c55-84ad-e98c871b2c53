/*
 * style.css
 *
 * Minimal dark theme for the mining NUI.  Uses a simple column layout with a
 * sidebar for navigation and a content area for dynamic panels.  Rounded
 * corners and subtle shadows provide a modern appearance.  Feel free to
 * customise colours and spacing to better fit your server's aesthetic.
 */

body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    color: #ddd;
}

#mining-app.hidden {
    display: none;
}

#mining-app {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 900px;
    height: 540px;
    display: flex;
    flex-direction: row;
    border-radius: 20px;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.85);
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.8);
    border: 2px solid #107c10;
}

.sidebar {
    width: 240px;
    background: #1e1e1e;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    position: relative;
    padding: 24px;
    box-shadow: inset -1px 0 0 0 #222;
}

.sidebar .title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 24px;
    color: #fff;
    text-align: center;
    padding-bottom: 12px;
    border-bottom: 1px solid #333;
}

/* Navigation buttons */
.tab-btn {
    background: transparent;
    border: none;
    color: #bbb;
    padding: 12px 16px;
    margin-bottom: 14px;
    border-radius: 8px;
    cursor: pointer;
    text-align: left;
    font-size: 18px;
    transition: background 0.2s, color 0.2s;
    width: 100%;
}

.tab-btn:hover {
    background: rgba(16, 124, 16, 0.2);
    color: #fff;
}

.tab-btn.active {
    background: rgba(16, 124, 16, 0.5);
    color: #fff;
}

/* Close button */
.close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    color: #888;
    font-size: 26px;
    cursor: pointer;
    transition: color 0.2s;
}

.close-btn:hover {
    color: #fff;
}

/* Profile information */
.profile {
    margin-top: auto;
    font-size: 14px;
    text-align: center;
    padding-top: 14px;
    border-top: 1px solid #333;
    color: #888;
}

/* Content panel */
.content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background: #121212;
}

/* Card layout */
.card {
    background: rgba(30, 30, 30, 0.8);
    border: 1px solid #444;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card .info {
    flex: 1;
    margin-right: 20px;
}

.card button {
    background: #107c10;
    border: none;
    color: #fff;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.2s;
}

.card button:hover {
    background: #0b5d0b;
}

.card input[type="number"] {
    width: 70px;
    padding: 8px;
    border: 1px solid #444;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    margin-right: 10px;
}

/* Typography for content headings and paragraphs */
.content h2 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 24px;
    font-weight: 500;
    color: #fff;
}

.content p {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 16px;
    color: #bbb;
}

/* Toast notifications */
.toast {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.85);
    color: #fff;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
    z-index: 1000;
}