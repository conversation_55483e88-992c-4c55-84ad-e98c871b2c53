--[[
    client/cl_peds.lua

    Handles creation and deletion of NPCs (peds) used for the mining system.  Peds are
    spawned at configured positions with their models loaded.  They are made invincible,
    frozen, and assigned idle scenarios to feel alive.  When the resource stops the
    peds are cleaned up.
--]]

local spawnedPeds = {}

local function loadModel(model)
    if not IsModelInCdimage(model) then return false end
    RequestModel(model)
    local timeout = GetGameTimer() + 5000
    while not HasModelLoaded(model) do
        Wait(50)
        if GetGameTimer() > timeout then
            return false
        end
    end
    return true
end

-- Spawn all configured peds
function SpawnMiningPeds()
    for name, data in pairs(Config.Peds) do
        if type(data.coords) == 'vector4' and loadModel(data.model) then
            local ped = CreatePed(0, data.model, data.coords.x, data.coords.y, data.coords.z - 1.0, data.coords.w, false, true)
            -- Freeze and make invincible.  Additional flags ensure the ped
            -- cannot be knocked down, attacked or react to player actions.
            SetEntityInvincible(ped, true)
            SetEntityCanBeDamaged(ped, false)
            FreezeEntityPosition(ped, true)
            SetEntityNoCollisionEntity(PlayerPedId(), ped, false)
            -- Prevent ped from reacting to any events (e.g. being hit)
            SetBlockingOfNonTemporaryEvents(ped, true)
            SetPedCanRagdoll(ped, false)
            SetPedCanBeDraggedOut(ped, false)
            SetPedCanBeKnockedOffVehicle(ped, false)
            SetPedFleeAttributes(ped, 0, 0)
            SetPedKeepTask(ped, true)
            -- assign idle scenario based on ped type
            local scenario
            if name == 'Shop' then
                scenario = 'WORLD_HUMAN_CLIPBOARD'
            elseif name == 'Smelt' then
                scenario = 'WORLD_HUMAN_STAND_IMPATIENT_UPRIGHT'
            elseif name == 'Sell' then
                scenario = 'WORLD_HUMAN_CLIPBOARD'
            end
            if scenario then
                TaskStartScenarioInPlace(ped, scenario, 0, true)
            end
            spawnedPeds[name] = ped
        end
    end
    -- Expose table globally for other modules (e.g., target)
    _G.spawnedPeds = spawnedPeds
end

-- Delete all spawned peds
function DeleteMiningPeds()
    for name, ped in pairs(spawnedPeds) do
        if DoesEntityExist(ped) then
            DeletePed(ped)
        end
    end
    spawnedPeds = {}
end

-- Export functions so they can be called from other scripts
exports('SpawnMiningPeds', SpawnMiningPeds)
exports('DeleteMiningPeds', DeleteMiningPeds)