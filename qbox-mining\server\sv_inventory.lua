--[[
    server/sv_inventory.lua

    Provides helper functions to interact with ox_inventory.  All inventory
    operations should be routed through this module to simplify error handling
    and maintain server authority.  If you wish to support additional inventory
    systems you can add wrapper functions here.
--]]

local Inventory = {}

-- Returns the slot, item data and metadata of the best pickaxe in the player's
-- inventory.  Best is determined by the highest tier (fastest speed) defined
-- in Config.Pickaxes.  Returns nil if no pickaxe is found.
function Inventory.GetBestPickaxe(src)
    local bestSlot, bestItem, bestMetadata
    for name, def in pairs(Config.Pickaxes) do
        -- Search for this pickaxe in the player's slots
        local results = exports.ox_inventory:Search(src, 'slots', name)
        if results and #results > 0 then
            local slot = results[1].slot
            local item = results[1]
            local metadata = item.metadata or {}
            if not bestSlot or (def.speed < Config.Pickaxes[bestItem.name].speed) then
                bestSlot, bestItem, bestMetadata = slot, item, metadata
            end
        end
    end
    return bestSlot, bestItem, bestMetadata
end

-- Count how many of a given item the player has
function Inventory.CountItem(src, itemName)
    local total = 0
    local list = exports.ox_inventory:Search(src, 'count', itemName)
    if type(list) == 'table' then return list.count or 0 end
    return list or 0
end

-- Give an item to the player
function Inventory.GiveItem(src, itemName, amount, metadata)
    metadata = metadata or {}
    return exports.ox_inventory:AddItem(src, itemName, amount or 1, metadata)
end

-- Remove an item from the player
function Inventory.RemoveItem(src, itemName, amount, metadata)
    return exports.ox_inventory:RemoveItem(src, itemName, amount or 1, metadata)
end

-- Update metadata for an item slot
function Inventory.UpdateItemMetadata(src, slot, metadata)
    return exports.ox_inventory:SetMetadata(src, slot, metadata)
end

return Inventory