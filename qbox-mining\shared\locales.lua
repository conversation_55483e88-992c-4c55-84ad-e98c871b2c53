--[[
    shared/locales.lua

    Locale loading helper.  This file loads the locale table defined in locales/<lang>.lua
    and exposes a simple function `L(key, ...)` to translate strings with optional
    placeholder replacement.  On the server the locale is stored in the environment table
    for global access; on the client it is stored in a local variable.
--]]

local localeCode = Config and Config.Locale or 'en'
local localeFile = string.format('locales/%s.lua', localeCode)

-- Try to load the locale file.  If it fails fallback to English.
local ok, localeTable = pcall(require, localeFile:gsub('%.lua', ''))
if not ok or type(localeTable) ~= 'table' then
    print(('qbox-mining: Failed to load locale "%s", falling back to English'):format(localeCode))
    ok, localeTable = pcall(require, 'locales/en')
    if not ok or type(localeTable) ~= 'table' then
        error('qbox-mining: missing default locale en.lua')
    end
end

-- Translation function.  Accepts a key and optional arguments to be formatted into the
-- string.  Placeholders should use `%s` in the locale definitions.
function L(key, ...)
    local str = localeTable[key] or key
    if select('#', ...) > 0 then
        return str:format(...)
    end
    return str
end

-- Export locale table for debugging or external usage
Locales = localeTable

return L