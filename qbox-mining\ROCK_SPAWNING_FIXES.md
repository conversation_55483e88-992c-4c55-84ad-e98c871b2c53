# Rock Spawning System Fixes and Improvements

## Overview

This document outlines the comprehensive fixes and improvements made to the rock spawning system in qbox-mining. The changes address critical issues with collision detection, ground positioning, spawn distribution, and performance optimization.

## Issues Identified and Fixed

### 1. **No Collision Detection Between Nodes**
**Problem:** Rocks could spawn overlapping each other, making them inaccessible or causing visual glitches.

**Solution:** 
- Added `CheckPositionCollision()` function that validates minimum distance between nodes
- Configurable minimum distance via `Config.Spawns.minDistanceBetweenNodes`
- Default minimum distance set to 8.0 game units

### 2. **Inconsistent Ground Height Detection**
**Problem:** Server-side `GetGroundZFor_3dCoord` frequently failed, causing rocks to spawn at incorrect heights.

**Solution:**
- Enhanced `findGroundZ()` function with multiple height attempts
- Added validation to ensure ground height is reasonable (within 50 units of expected)
- Improved fallback mechanism for failed ground detection

### 3. **Spawn Area Configuration Mismatch**
**Problem:** Hard-coded spawn center and radius ignored configuration values.

**Solution:**
- Modified `ComputeSpawnArea()` to use `Config.Spawns.spawnCenter` and `Config.Spawns.spawnRadius`
- Added proper initialization and validation
- Added logging for spawn area initialization

### 4. **High Node Density Issues**
**Problem:** 200 nodes in 100-unit radius caused overcrowding and performance issues.

**Solution:**
- Reduced default `maxSpawnedNodes` from 200 to 75
- Added intelligent spawn attempt limiting
- Improved spacing with configurable minimum distances

### 5. **Poor Spawn Distribution**
**Problem:** Random polar coordinate distribution could create clustering.

**Solution:**
- Added grid-based spawning algorithm as an option
- Configurable via `Config.Spawns.useGridBasedSpawning`
- Spiral pattern generation for even coverage
- Random offsets within grid cells to avoid perfect alignment

## New Configuration Options

### Added to `Config.Spawns`:

```lua
-- Collision detection and spacing settings
minDistanceBetweenNodes = 8.0, -- Minimum distance between rock spawns (in game units)
maxSpawnAttempts = 500, -- Maximum attempts to find a valid spawn position
groundCheckRadius = 2.0, -- Radius to check for suitable ground around spawn point

-- Distribution algorithm settings
useGridBasedSpawning = true, -- Use grid-based distribution for better coverage
gridCellSize = 12.0, -- Size of each grid cell (should be larger than minDistanceBetweenNodes)
randomOffsetRange = 4.0, -- Random offset within each grid cell to avoid perfect alignment

-- Spawn area configuration - circular area centered on the quarry
spawnCenter = vector3(2955.7224, 2748.6758, 43.5263), -- Center of the quarry
spawnRadius = 100.0, -- Maximum distance from center where rocks can spawn
```

### Modified Settings:
- `maxSpawnedNodes`: Reduced from 200 to 75 for better performance and spacing

## Technical Changes

### New Functions Added:

1. **`CheckPositionCollision(pos, minDistance)`**
   - Validates minimum distance between spawn positions
   - Prevents overlapping nodes

2. **`IsValidSpawnPosition(pos)`**
   - Validates spawn position is within allowed area
   - Extensible for additional validation rules

3. **`GenerateGridBasedPositions(maxNodes)`**
   - Grid-based spawn position generation
   - Spiral pattern for even distribution
   - Random offsets for natural appearance

### Enhanced Functions:

1. **`findGroundZ(x, y, defaultZ)`**
   - Multiple height attempt strategy
   - Ground height validation
   - Improved fallback mechanism

2. **`ComputeSpawnArea()`**
   - Configuration-driven initialization
   - Proper logging and validation

3. **`SpawnInitialNodes()`**
   - Collision detection integration
   - Support for both random and grid-based distribution
   - Comprehensive logging and progress tracking
   - Intelligent failure handling

## Performance Improvements

1. **Reduced Node Count:** Lower default spawn count reduces entity overhead
2. **Efficient Collision Detection:** O(n) collision checking with early termination
3. **Smart Spawn Attempts:** Configurable attempt limits prevent infinite loops
4. **Grid-Based Distribution:** More efficient than pure random for large node counts

## Logging and Debugging

Added comprehensive logging throughout the spawn process:
- Spawn area initialization details
- Progress tracking during spawning
- Warning messages for configuration issues
- Performance metrics (spawn count vs attempts)

## Backward Compatibility

All changes maintain backward compatibility:
- Existing configuration values are preserved
- New options have sensible defaults
- Original random spawning method remains available
- No breaking changes to existing APIs

## Recommended Settings

For optimal performance and appearance:

```lua
Config.Spawns = {
    maxSpawnedNodes = 75,
    minDistanceBetweenNodes = 8.0,
    useGridBasedSpawning = true,
    gridCellSize = 12.0,
    randomOffsetRange = 4.0,
    maxSpawnAttempts = 500,
    spawnCenter = vector3(2955.7224, 2748.6758, 43.5263),
    spawnRadius = 100.0,
    respawnTime = { min = 45000, max = 60000 },
    playerCooldown = 5000
}
```

## Testing

A test script (`test_spawn_logic.lua`) has been created to validate:
- Collision detection functionality
- Ground height calculation
- Ore distribution by distance
- Basic spawn logic validation

## Future Enhancements

Potential improvements for future versions:
1. Dynamic spawn density based on player count
2. Terrain-aware spawning (avoid steep slopes, water)
3. Biome-specific ore distribution
4. Real-time spawn adjustment based on mining activity
5. Integration with zone-based spawning restrictions

## Migration Guide

No migration is required. The system will automatically use the new logic on next resource restart. Server administrators may want to:

1. Review and adjust `maxSpawnedNodes` if needed
2. Enable grid-based spawning for better distribution
3. Tune `minDistanceBetweenNodes` based on rock model sizes
4. Monitor server logs for spawn warnings

## Conclusion

These improvements significantly enhance the rock spawning system's reliability, performance, and visual quality. The new collision detection prevents overlapping issues, improved ground detection ensures proper placement, and the grid-based distribution provides better coverage of the mining area.
