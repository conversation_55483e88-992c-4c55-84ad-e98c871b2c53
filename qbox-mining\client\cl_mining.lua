--[[
    client/cl_mining.lua

    Contains logic for interacting with mining nodes.  Handles client side cooldowns,
    animations, and communicates with the server for reward validation.  Also manages
    spawning and despawning of visual rock props representing ore nodes.
--]]

local lastMineAttempt = 0
-- Track spawned nodes.  Each entry holds the created prop entity, its ore type
-- and the associated target ID (if any) so we can remove it when despawning.
local spawnedNodes = {}

local debugEnabled = false

-- Toggle debug mode when admin uses /mining:debug
RegisterNetEvent('qbox-mining:client:toggleDebug', function()
    debugEnabled = not debugEnabled
    local msg = debugEnabled and L('admin_debug_on') or L('admin_debug_off')
    lib.notify({ description = msg, type = 'inform' })
end)

-- Draw debug info on screen
local function DrawText3D(x, y, z, text)
    SetDrawOrigin(x, y, z, 0)
    SetTextScale(0.0, 0.35)
    SetTextFont(0)
    SetTextProportional(true)
    SetTextColour(255, 255, 255, 215)
    SetTextCentre(true)
    BeginTextCommandDisplayText('STRING')
    AddTextComponentSubstringPlayerName(text)
    EndTextCommandDisplayText(0.0, 0.0)
    ClearDrawOrigin()
end

Citizen.CreateThread(function()
    while true do
        if debugEnabled then
            for id, data in pairs(spawnedNodes) do
                local coords = GetEntityCoords(data.obj)
                DrawText3D(coords.x, coords.y, coords.z + 1.0, ('ID: %s\nOre: %s'):format(id, data.ore))
            end
        end
        Wait(0)
    end
end)

-- Spawn or remove node props based on server state
function UpdateNodeEntities(nodes)
    -- Remove any nodes that no longer exist
    for id, data in pairs(spawnedNodes) do
        if not nodes[id] then
            if data.obj and DoesEntityExist(data.obj) then
                -- remove any registered target before deleting the entity
                if data.target and Config.UseOxTarget and GetResourceState('ox_target') ~= 'missing' then
                    exports.ox_target:removeLocalEntity(data.obj)
                elseif data.target and GetResourceState('qb-target') ~= 'missing' then
                    exports['qb-target']:RemoveTargetEntity(data.obj)
                end
                DeleteEntity(data.obj)
            end
            spawnedNodes[id] = nil
        end
    end
    -- Add or update nodes from server
    for id, info in pairs(nodes) do
        if not spawnedNodes[id] then
            -- spawn prop
            local model = GetHashKey(info.model or 'prop_rock_4_g')
            RequestModel(model)
            while not HasModelLoaded(model) do Wait(10) end
            local pos = info.coords
            local obj = CreateObject(model, pos.x, pos.y, pos.z - 0.0, false, false, false)
            SetEntityAsMissionEntity(obj, true, true)
            PlaceObjectOnGroundProperly(obj)
            -- Freeze the rock so players cannot bump it and make it fall through the world
            FreezeEntityPosition(obj, true)
            -- Attach an interactive target so the player can start mining by looking at the rock
            local targetId
            local oreLabel = info.ore
            -- Capitalise ore name for label
            oreLabel = oreLabel:gsub("^%l", string.upper)
            if Config.UseOxTarget and GetResourceState('ox_target') ~= 'missing' then
                exports.ox_target:addLocalEntity(obj, {
                    {
                        label = ('Mine %s'):format(oreLabel),
                        icon = 'fa-solid fa-hammer',
                        onSelect = function()
                            TriggerEvent('qbox-mining:client:mineNode', id, info.ore)
                        end
                    }
                })
                targetId = obj -- we store the entity itself for removal later
            elseif GetResourceState('qb-target') ~= 'missing' then
                exports['qb-target']:AddTargetEntity(obj, {
                    options = {
                        {
                            label = ('Mine %s'):format(oreLabel),
                            icon = 'fa-solid fa-hammer',
                            action = function()
                                TriggerEvent('qbox-mining:client:mineNode', id, info.ore)
                            end
                        }
                    },
                    distance = 2.0
                })
                targetId = obj
            end
            spawnedNodes[id] = { obj = obj, ore = info.ore, target = targetId }
        end
    end
end

-- Clear all node props on resource stop
function ClearSpawnedNodes()
    for id, data in pairs(spawnedNodes) do
        if data.obj and DoesEntityExist(data.obj) then
            -- remove any registered targets before deleting
            if data.target and Config.UseOxTarget and GetResourceState('ox_target') ~= 'missing' then
                exports.ox_target:removeLocalEntity(data.obj)
            elseif data.target and GetResourceState('qb-target') ~= 'missing' then
                exports['qb-target']:RemoveTargetEntity(data.obj)
            end
            DeleteEntity(data.obj)
        end
    end
    spawnedNodes = {}
end

-- Begin the mining process on a specific node.  The server determines validity and
-- returns details such as duration and speed modifiers.  Cooldowns are applied on
-- both client and server.
function StartMining(nodeId, oreId)
    local now = GetGameTimer()
    local cooldown = Config.Spawns.playerCooldown or 0
    -- enforce client‑side cooldown only if lastMineAttempt is within the configured window
    if now - lastMineAttempt < cooldown then
        lib.notify({ description = L('mining_on_cooldown'), type = 'error' })
        return
    end
    -- Save the previous attempt time so we can revert on cancellation
    local previousAttempt = lastMineAttempt
    -- Ask server to validate mining attempt
    local result = lib.callback.await('qbox-mining:server:startMining', false, nodeId)
    if not result or not result.success then
        if result and result.message then
            lib.notify({ description = result.message, type = 'error' })
        end
        return
    end
    -- Server accepted the mining attempt, update our local cooldown timestamp
    lastMineAttempt = now
    -- Play the mining animation and progress bar
    local finished = exports['qbox-mining']:PlayMiningAction(result.duration, result.speed)
    if finished then
        -- Inform server that mining finished successfully
        TriggerServerEvent('qbox-mining:server:finishMining', nodeId)
    else
        -- Cancelled; inform server to reset server‑side cooldown and revert our client cooldown
        lastMineAttempt = previousAttempt
        TriggerServerEvent('qbox-mining:server:cancelMining', nodeId)
    end
end

exports('UpdateNodeEntities', UpdateNodeEntities)
exports('ClearSpawnedNodes', ClearSpawnedNodes)
exports('StartMining', StartMining)