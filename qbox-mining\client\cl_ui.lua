--[[
    client/cl_ui.lua

    Manages the custom NUI used for the mining shop, smelting and selling interfaces.
    The UI is a small single‑page app served from the web/ directory.  All state and
    transactions are handled on the server; the client simply forwards user actions
    through lib.callback.  ESC closes the UI.
--]]

local uiOpen = false
local currentTab = nil
local playerProfile = nil
local allowedTabs = nil

function OpenMiningUi(tab, extra)
    -- Prevent opening multiple instances
    if uiOpen then return end
    uiOpen = true
    currentTab = tab or 'shop'
    -- Determine allowed tabs from the caller.  If none provided the UI
    -- will display all sections.  This allows limiting functionality on
    -- specific peds (e.g. only smelting at the smelter).
    allowedTabs = nil
    if extra and type(extra.allowedTabs) == 'table' then
        allowedTabs = extra.allowedTabs
    end
    SetNuiFocus(true, true)
    -- Provide config data to the NUI on open for displaying pickaxes, upgrades, recipes and prices
    local cfg = {
        pickaxes = Config.Pickaxes,
        upgrades = Config.Upgrades,
        smelting = Config.Smelting,
        sellPrices = Config.SellPrices
    }
    SendNUIMessage({
        action = 'open',
        tab = currentTab,
        profile = playerProfile,
        config = cfg,
        allowedTabs = allowedTabs
    })
end

-- Closes the NUI
function CloseMiningUi()
    if not uiOpen then return end
    uiOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({ action = 'close' })
end

-- Receive updated profile from server and cache it locally
function SetLocalProfile(profile)
    playerProfile = profile
    -- Send profile to UI if open
    if uiOpen then
        SendNUIMessage({ action = 'profile', profile = playerProfile })
    end
end

-- Update XP/level and refresh UI
function OnXpUpdate(level, xp)
    if not playerProfile then return end
    playerProfile.level = level
    playerProfile.xp = xp
    if uiOpen then
        SendNUIMessage({ action = 'profile', profile = playerProfile })
    end
end

-- NUI Callbacks
RegisterNUICallback('close', function(_, cb)
    CloseMiningUi()
    cb(true)
end)

RegisterNUICallback('purchasePickaxe', function(data, cb)
    -- data contains { pickaxe = 'pickaxe_basic' }
    local success, message = lib.callback.await('qbox-mining:server:purchasePickaxe', false, data.pickaxe)
    cb({ success = success, message = message })
end)

RegisterNUICallback('upgradePickaxe', function(data, cb)
    -- data contains { pickaxe = 'pickaxe_basic', upgrade = 'speed' }
    local success, message = lib.callback.await('qbox-mining:server:upgradePickaxe', false, data.pickaxe, data.upgrade)
    cb({ success = success, message = message })
end)

RegisterNUICallback('repairPickaxe', function(data, cb)
    -- data contains { pickaxe = 'pickaxe_basic' }
    local success, message = lib.callback.await('qbox-mining:server:repairPickaxe', false, data.pickaxe)
    cb({ success = success, message = message })
end)

RegisterNUICallback('smelt', function(data, cb)
    -- data contains { recipeIndex = n, quantity = m }
    local success, message = lib.callback.await('qbox-mining:server:startSmelting', false, data.recipeIndex, data.quantity)
    cb({ success = success, message = message })
end)

RegisterNUICallback('sellItems', function(data, cb)
    -- data contains { items = { { item = 'ingot_copper', qty = 5 }, ... } }
    local success, message = lib.callback.await('qbox-mining:server:sellItems', false, data.items)
    cb({ success = success, message = message })
end)

-- Register key to close UI on ESC
Citizen.CreateThread(function()
    while true do
        if uiOpen and IsControlJustPressed(0, 322) then -- ESC key
            CloseMiningUi()
        end
        Wait(0)
    end
end)

exports('OpenMiningUi', OpenMiningUi)
exports('CloseMiningUi', CloseMiningUi)
exports('SetLocalProfile', SetLocalProfile)
exports('OnXpUpdate', OnXpUpdate)