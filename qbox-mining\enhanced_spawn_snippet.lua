-- Enhanced version of the spawn snippet with collision detection and other improvements
function SpawnRocksWithCollisionDetection(center, radius, count, minDistance)
    minDistance = minDistance or 8.0 -- Default minimum distance between rocks
    
    -- Define the ore tiers based on distance from the centre
    local oreRings = {
        { 0.00, 0.10, 'titanium' }, -- 0–10% of radius: rare
        { 0.10, 0.30, 'gold'     }, -- 10–30%: uncommon
        { 0.30, 0.50, 'aluminum' }, -- 30–50%: mid
        { 0.50, 0.75, 'copper'   }, -- 50–75%: common
        { 0.75, 1.00, 'iron'     }, -- 75–100%: very common
    }

    -- Helper to pick an ore type based on normalized distance from centre
    local function getOreByRatio(ratio)
        for _, ring in ipairs(oreRings) do
            if ratio >= ring[1] and ratio < ring[2] then
                return ring[3]
            end
        end
        return 'iron'
    end

    -- Helper to generate a random point inside a circle uniformly
    local function randomPointInCircle(cx, cy, r)
        local angle = math.random() * (2.0 * math.pi)
        local dist  = r * math.sqrt(math.random())
        return cx + dist * math.cos(angle), cy + dist * math.sin(angle), dist
    end

    -- Enhanced ground finding with multiple attempts
    local function findGroundZ(x, y, defaultZ)
        local startHeights = { defaultZ + 50.0, defaultZ + 100.0, defaultZ + 20.0, defaultZ }
        
        for _, startZ in ipairs(startHeights) do
            local ok, groundZ = GetGroundZFor_3dCoord(x, y, startZ, true)
            if ok and groundZ then
                -- Validate that the ground height is reasonable
                local heightDiff = math.abs(groundZ - defaultZ)
                if heightDiff < 50.0 then
                    return groundZ
                end
            end
        end
        
        return defaultZ -- Fallback to default
    end

    -- Collision detection function
    local function checkCollision(newPos, existingRocks, minDist)
        for _, rock in ipairs(existingRocks) do
            local dx = newPos.x - rock.coords.x
            local dy = newPos.y - rock.coords.y
            local distance = math.sqrt(dx * dx + dy * dy)
            
            if distance < minDist then
                return true -- Collision detected
            end
        end
        return false -- No collision
    end

    -- Request the rock model
    local rockModel = GetHashKey('prop_rock_1_f')
    RequestModel(rockModel)
    while not HasModelLoaded(rockModel) do
        Wait(10)
    end

    local spawned = {}
    local attempts = 0
    local maxAttempts = count * 10 -- Prevent infinite loops
    
    print(string.format("Starting spawn: target=%d rocks, minDistance=%.1f", count, minDistance))
    
    while #spawned < count and attempts < maxAttempts do
        attempts = attempts + 1
        
        -- Pick a random position within the circle
        local x, y, dist = randomPointInCircle(center.x, center.y, radius)
        local z = findGroundZ(x, y, center.z)
        local pos = vector3(x, y, z)
        
        -- Check for collisions with existing rocks
        if not checkCollision(pos, spawned, minDistance) then
            -- Create the rock object
            local prop = CreateObject(rockModel, x, y, z, false, false, false)
            PlaceObjectOnGroundProperly(prop)
            FreezeEntityPosition(prop, true)

            -- Compute ore tier based on distance ratio
            local ratio = dist / radius
            local ore = getOreByRatio(ratio)

            table.insert(spawned, { coords = pos, ore = ore, prop = prop })
            
            -- Log progress
            if #spawned % 10 == 0 then
                print(string.format("Spawned %d/%d rocks (attempts: %d)", #spawned, count, attempts))
            end
        end
    end
    
    print(string.format("Spawn complete: %d/%d rocks spawned in %d attempts", #spawned, count, attempts))
    
    if #spawned < count then
        print(string.format("WARNING: Could only spawn %d out of %d requested rocks. Consider reducing count or minDistance.", #spawned, count))
    end

    return spawned
end

-- Grid-based alternative for even better distribution
function SpawnRocksGridBased(center, radius, count, cellSize, offsetRange)
    cellSize = cellSize or 12.0
    offsetRange = offsetRange or 4.0
    
    -- Same ore rings as above
    local oreRings = {
        { 0.00, 0.10, 'titanium' },
        { 0.10, 0.30, 'gold'     },
        { 0.30, 0.50, 'aluminum' },
        { 0.50, 0.75, 'copper'   },
        { 0.75, 1.00, 'iron'     },
    }

    local function getOreByRatio(ratio)
        for _, ring in ipairs(oreRings) do
            if ratio >= ring[1] and ratio < ring[2] then
                return ring[3]
            end
        end
        return 'iron'
    end

    local function findGroundZ(x, y, defaultZ)
        local startHeights = { defaultZ + 50.0, defaultZ + 100.0, defaultZ + 20.0, defaultZ }
        for _, startZ in ipairs(startHeights) do
            local ok, groundZ = GetGroundZFor_3dCoord(x, y, startZ, true)
            if ok and groundZ then
                local heightDiff = math.abs(groundZ - defaultZ)
                if heightDiff < 50.0 then
                    return groundZ
                end
            end
        end
        return defaultZ
    end

    -- Generate grid positions in spiral pattern
    local gridRadius = math.ceil(radius / cellSize)
    local gridPositions = {}
    
    -- Start from center and spiral outward
    for r = 0, gridRadius do
        if r == 0 then
            table.insert(gridPositions, {x = 0, y = 0})
        else
            local circumference = 8 * r
            for i = 1, circumference do
                local angle = (i / circumference) * 2 * math.pi
                local gridX = math.floor(r * math.cos(angle) + 0.5)
                local gridY = math.floor(r * math.sin(angle) + 0.5)
                
                -- Check uniqueness
                local unique = true
                for _, existing in ipairs(gridPositions) do
                    if existing.x == gridX and existing.y == gridY then
                        unique = false
                        break
                    end
                end
                
                if unique then
                    table.insert(gridPositions, {x = gridX, y = gridY})
                end
            end
        end
    end

    -- Request model
    local rockModel = GetHashKey('prop_rock_1_f')
    RequestModel(rockModel)
    while not HasModelLoaded(rockModel) do
        Wait(10)
    end

    local spawned = {}
    
    for i = 1, math.min(#gridPositions, count) do
        local gridPos = gridPositions[i]
        
        -- Calculate world position with random offset
        local baseX = center.x + (gridPos.x * cellSize)
        local baseY = center.y + (gridPos.y * cellSize)
        local offsetX = (math.random() - 0.5) * offsetRange
        local offsetY = (math.random() - 0.5) * offsetRange
        local worldX = baseX + offsetX
        local worldY = baseY + offsetY
        
        -- Check if within radius
        local dx = worldX - center.x
        local dy = worldY - center.y
        local distance = math.sqrt(dx * dx + dy * dy)
        
        if distance <= radius then
            local z = findGroundZ(worldX, worldY, center.z)
            
            -- Create rock
            local prop = CreateObject(rockModel, worldX, worldY, z, false, false, false)
            PlaceObjectOnGroundProperly(prop)
            FreezeEntityPosition(prop, true)
            
            -- Determine ore type
            local ratio = distance / radius
            local ore = getOreByRatio(ratio)
            
            table.insert(spawned, { coords = vector3(worldX, worldY, z), ore = ore, prop = prop })
        end
    end
    
    print(string.format("Grid-based spawn complete: %d rocks spawned", #spawned))
    return spawned
end

-- Usage examples:
--[[
-- With collision detection (random distribution)
local rocks1 = SpawnRocksWithCollisionDetection(
    vector3(2955.7, 2748.7, 43.5), -- center
    100.0,                          -- radius
    50,                             -- count
    8.0                             -- minimum distance
)

-- Grid-based distribution (even coverage)
local rocks2 = SpawnRocksGridBased(
    vector3(2955.7, 2748.7, 43.5), -- center
    100.0,                          -- radius
    50,                             -- count
    12.0,                           -- cell size
    4.0                             -- random offset range
)
--]]
