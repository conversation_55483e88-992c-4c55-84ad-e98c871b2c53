--[[
    test_spawn_logic.lua
    
    Simple test script to validate the rock spawning logic changes.
    This can be run to check for syntax errors and basic functionality.
]]

-- Mock Config for testing
local Config = {
    Spawns = {
        maxSpawnedNodes = 75,
        minDistanceBetweenNodes = 8.0,
        maxSpawnAttempts = 500,
        groundCheckRadius = 2.0,
        useGridBasedSpawning = true,
        gridCellSize = 12.0,
        randomOffsetRange = 4.0,
        spawnCenter = vector3(2955.7224, 2748.6758, 43.5263),
        spawnRadius = 100.0,
        respawnTime = { min = 45000, max = 60000 },
        playerCooldown = 5000
    },
    Ores = {
        iron = { model = 'prop_rock_4_g', item = 'ore_iron', xp = 10 },
        copper = { model = 'prop_rock_4_g', item = 'ore_copper', xp = 15 },
        aluminum = { model = 'prop_rock_4_g', item = 'ore_aluminum', xp = 20 },
        gold = { model = 'prop_rock_4_g', item = 'ore_gold', xp = 30 },
        titanium = { model = 'prop_rock_4_g', item = 'ore_titanium', xp = 50 }
    }
}

-- Mock vector3 function
function vector3(x, y, z)
    return { x = x, y = y, z = z }
end

-- Mock FiveM natives
function GetGroundZFor_3dCoord(x, y, z)
    -- Simulate successful ground detection most of the time
    if math.random() > 0.1 then
        return true, 43.0 + math.random(-5, 5) -- Simulate ground variation
    else
        return false, nil
    end
end

function GetGameTimer()
    return os.time() * 1000
end

-- Test variables
local Nodes = {}
local NextNodeId = 1
local SpawnArea = {}
local SpawnCenter = nil
local SpawnRadius = nil

-- Test functions (simplified versions of the actual functions)
local function ComputeSpawnArea()
    SpawnCenter = Config.Spawns.spawnCenter or vector3(2955.7224, 2748.6758, 43.5263)
    SpawnRadius = Config.Spawns.spawnRadius or 100.0
    
    local cx, cy, cz = SpawnCenter.x, SpawnCenter.y, SpawnCenter.z
    SpawnArea.minX = cx - SpawnRadius
    SpawnArea.maxX = cx + SpawnRadius
    SpawnArea.minY = cy - SpawnRadius
    SpawnArea.maxY = cy + SpawnRadius
    SpawnArea.minZ = cz - 10.0
    SpawnArea.maxZ = cz + 10.0
    SpawnArea.centerX = cx
    SpawnArea.centerY = cy
    SpawnArea.maxRadius = SpawnRadius
    
    print(string.format("Spawn area initialized: Center(%.2f, %.2f, %.2f), Radius: %.2f", 
        cx, cy, cz, SpawnRadius))
end

local function findGroundZ(x, y, defaultZ)
    local baseZ = defaultZ or (SpawnCenter and SpawnCenter.z or 43.0)
    local startHeights = { baseZ + 50.0, baseZ + 100.0, baseZ + 20.0, baseZ }
    
    for _, startZ in ipairs(startHeights) do
        local ok, groundZ = GetGroundZFor_3dCoord(x + 0.0, y + 0.0, startZ + 0.0)
        if ok and groundZ then
            local heightDiff = math.abs(groundZ - baseZ)
            if heightDiff < 50.0 then
                return groundZ
            end
        end
    end
    
    return baseZ
end

local function GetOreByPosition(pos)
    if not SpawnArea.maxRadius or SpawnArea.maxRadius == 0 then
        local keys = {}
        for oreId in pairs(Config.Ores) do keys[#keys + 1] = oreId end
        return keys[math.random(#keys)]
    end
    
    local dx = pos.x - SpawnArea.centerX
    local dy = pos.y - SpawnArea.centerY
    local dist = math.sqrt(dx * dx + dy * dy)
    local ratio = dist / SpawnArea.maxRadius
    
    if ratio >= 0.75 then return 'iron' end
    if ratio >= 0.50 then return 'copper' end
    if ratio >= 0.30 then return 'aluminum' end
    if ratio >= 0.10 then return 'gold' end
    return 'titanium'
end

local function CheckPositionCollision(pos, minDistance)
    minDistance = minDistance or (Config.Spawns.minDistanceBetweenNodes or 8.0)
    
    for _, node in pairs(Nodes) do
        if node.active and node.coords then
            local dx = pos.x - node.coords.x
            local dy = pos.y - node.coords.y
            local distance = math.sqrt(dx * dx + dy * dy)
            
            if distance < minDistance then
                return true
            end
        end
    end
    
    return false
end

local function IsValidSpawnPosition(pos)
    if SpawnCenter then
        local dx = pos.x - SpawnCenter.x
        local dy = pos.y - SpawnCenter.y
        local distance = math.sqrt(dx * dx + dy * dy)
        
        if distance > SpawnRadius then
            return false
        end
    end
    
    return true
end

local function SpawnNode(pos)
    local id = NextNodeId
    NextNodeId = NextNodeId + 1
    local ore = GetOreByPosition(pos)
    local oreDef = Config.Ores[ore] or {}
    local model = oreDef.model or 'prop_rock_4_g'
    Nodes[id] = {
        id = id,
        coords = pos,
        ore = ore,
        active = true,
        respawnAt = 0,
        model = model
    }
end

-- Test the spawning logic
local function TestSpawnLogic()
    print("=== Testing Rock Spawning Logic ===")
    
    -- Initialize spawn area
    ComputeSpawnArea()
    
    -- Test collision detection
    print("\n--- Testing Collision Detection ---")
    local testPos1 = vector3(2955, 2748, 43)
    local testPos2 = vector3(2960, 2748, 43) -- 5 units away
    local testPos3 = vector3(2965, 2748, 43) -- 10 units away
    
    SpawnNode(testPos1)
    print("Spawned test node at (2955, 2748, 43)")
    
    print("Collision check for pos 5 units away:", CheckPositionCollision(testPos2, 8.0))
    print("Collision check for pos 10 units away:", CheckPositionCollision(testPos3, 8.0))
    
    -- Test ground finding
    print("\n--- Testing Ground Finding ---")
    for i = 1, 5 do
        local x = 2950 + math.random(10)
        local y = 2740 + math.random(10)
        local z = findGroundZ(x, y, 43.0)
        print(string.format("Ground Z at (%.1f, %.1f): %.2f", x, y, z))
    end
    
    -- Test ore distribution
    print("\n--- Testing Ore Distribution ---")
    local oreCount = { iron = 0, copper = 0, aluminum = 0, gold = 0, titanium = 0 }
    for i = 1, 100 do
        local angle = math.random() * (2.0 * math.pi)
        local radius = SpawnRadius * math.sqrt(math.random())
        local x = SpawnCenter.x + radius * math.cos(angle)
        local y = SpawnCenter.y + radius * math.sin(angle)
        local pos = vector3(x, y, 43)
        local ore = GetOreByPosition(pos)
        oreCount[ore] = oreCount[ore] + 1
    end
    
    for ore, count in pairs(oreCount) do
        print(string.format("%s: %d%%", ore, count))
    end
    
    print("\n=== Test Complete ===")
    print("Total nodes created:", #Nodes)
end

-- Run the test
TestSpawnLogic()
