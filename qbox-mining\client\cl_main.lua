--[[
    client/cl_main.lua

    Entry point for client side logic.  This script initialises peds, target interactions,
    mining zones and the NUI.  It also listens for server events and forwards them to
    the appropriate modules.  No heavy processing should occur here; break logic into
    separate modules in client/ when possible.
--]]

local playerLoaded = false

-- Load locales into global environment
local L = L -- imported from shared/locales.lua

-- Shared exports for Qbox/QBCore detection.  Use wrappers to abstract away differences.
QBCore = nil
Qbox = nil

-- Framework detection
Citizen.CreateThread(function()
    if Config.UseQbox and GetResourceState('qbox-core') ~= 'missing' then
        Qbox = exports['qbox-core']
    elseif GetResourceState('qb-core') ~= 'missing' then
        QBCore = exports['qb-core']:GetCoreObject()
    end
end)

-- When the resource starts create peds and zones and register targets
AddEventHandler('onClientResourceStart', function(res)
    if res ~= GetCurrentResourceName() then return end
    -- Spawn NPCs
    exports['qbox-mining']:SpawnMiningPeds()
    -- Register target zones
    exports['qbox-mining']:RegisterPedTargets()
    -- Create mining zones
    exports['qbox-mining']:SetupMiningZones()
    -- Send player data request to server
    TriggerServerEvent('qbox-mining:server:requestProfile')
end)

-- On resource stop cleanup peds and any spawned objects
AddEventHandler('onClientResourceStop', function(res)
    if res ~= GetCurrentResourceName() then return end
    exports['qbox-mining']:DeleteMiningPeds()
    exports['qbox-mining']:ClearSpawnedNodes()
end)

-- Example callback for UI: open when interacting with peds
RegisterNetEvent('qbox-mining:client:openUi', function(data)
    -- data.tab indicates which UI to open: 'shop', 'smelt' or 'sell'
    exports['qbox-mining']:OpenMiningUi(data.tab, data.extra)
end)

-- Event to start mining at a node
RegisterNetEvent('qbox-mining:client:mineNode', function(nodeId, oreId)
    exports['qbox-mining']:StartMining(nodeId, oreId)
end)

-- Receive profile from server
RegisterNetEvent('qbox-mining:client:setProfile', function(profile)
    exports['qbox-mining']:SetLocalProfile(profile)
end)

-- Notify client of XP and level updates
RegisterNetEvent('qbox-mining:client:updateXP', function(newLevel, newXp)
    exports['qbox-mining']:OnXpUpdate(newLevel, newXp)
end)

-- Receive node update (spawn/despawn) from server
RegisterNetEvent('qbox-mining:client:updateNodes', function(nodes)
    exports['qbox-mining']:UpdateNodeEntities(nodes)
end)

---------------------------------------------------------------------------------------------------
-- Vehicle spawn suppression
--
-- Davis Quarry has static vehicle generators that spawn dump trucks around the rim.  To prevent
-- these heavy vehicles from interfering with mining, we periodically remove vehicles spawned by
-- generators within the mining area.  The area is defined using Config.Spawns.spawnBounds on the
-- client side in the same way as the server.  If spawnBounds is not configured this thread
-- does nothing.
Citizen.CreateThread(function()
    local function computeBounds()
        local bounds = Config.Spawns and Config.Spawns.spawnBounds
        if not bounds or #bounds < 1 then return nil end
        local minX, maxX = 99999, -99999
        local minY, maxY = 99999, -99999
        local minZ, maxZ = 99999, -99999
        for _, v in ipairs(bounds) do
            local x = v.x or v[1]
            local y = v.y or v[2]
            local z = v.z or v[3]
            if x < minX then minX = x end
            if x > maxX then maxX = x end
            if y < minY then minY = y end
            if y > maxY then maxY = y end
            if z < minZ then minZ = z end
            if z > maxZ then maxZ = z end
        end
        return { minX = minX, maxX = maxX, minY = minY, maxY = maxY, minZ = minZ, maxZ = maxZ }
    end
    local area = computeBounds()
    -- Remove vehicles from the area every 30 seconds
    while true do
        if area then
            RemoveVehiclesFromGeneratorsInArea(area.minX, area.minY, area.minZ, area.maxX, area.maxY, area.maxZ)
        end
        Wait(30000)
    end
end)