# Qbox Mining System

The **Qbox Mining** resource adds an immersive mining profession to your Qbox or QBCore‑powered FiveM server.  Players can mine ores in the Davis Quarry, smelt them into valuable ingots, sell their goods for cash and XP, purchase and upgrade pickaxes and even complete daily contracts.  The system is built on the modern `ox_lib` stack with optional integration into `ox_target` and `oxmysql`.

## Features

* **Dynamic mining nodes** – rocks spawn around configured zones.  Each node awards random ores based on rarity.  Respawns occur automatically.
* **Pickaxe tiers & upgrades** – three tiers of pickaxes with durability and upgrade paths for speed, yield and luck.  Tools can be repaired.
* **Smelting** – convert raw ores into ingots using the smelting interface.  Recipes, times and XP rewards are fully configurable.
* **Selling** – sell ingots for dynamic prices with tax and optional bonuses.  Revenue is deposited directly into the player’s account.
* **Level system** – earn XP from mining and smelting to level up (up to level 100).  Higher levels grant minor perks and unlock better gear.
* **Modern UI** – a dark, glassy interface built with the ox_lib context menu and a minimal NUI for richer screens.  Keyboard friendly.
* **Server‑authority** – all reward, smelt and sale calculations run on the server.  Cooldowns and validations protect against exploits.
* **Highly configurable** – most aspects (zones, ores, recipes, prices, upgrades, contracts, taxes) can be tweaked in `shared/config.lua`.

## Installation

1. **Clone or copy** the `qbox-mining` folder into your server’s `resources` directory.
2. Add the resource to your server configuration **after** `ox_lib` and `ox_inventory`:

   ```ini
   ensure ox_lib
   ensure ox_inventory
   ensure qbox-mining
   ```

3. **Register the items** in `ox_inventory/data/items.lua`.  The resource does **not** auto‑register its items because inventory registration must occur on server start.  Example entries:

   ```lua
   ['pickaxe_basic'] = {
       label = 'Basic Pickaxe',
       weight = 3.0,
       stack = false,
       close = false,
       description = 'A basic pickaxe used for mining.',
       consume = false,
       client = {},
       server = {}
   },
   ['ore_iron'] = {
       label = 'Iron Ore',
       weight = 1.0,
       stack = true,
       description = 'Unrefined iron ore.',
       icon = 'images/ore.png'
   },
   -- Add similar definitions for all ores, ingots, gems, geodes, and pickaxes
   ```

   You should also add icons for each item.  Place your PNG or SVG files in `qbox-mining/web/images/` and reference them in the item definitions (e.g. `icon = 'images/ingot_gold.png'`).

4. **Configure the resource** by editing `shared/config.lua`.  You can adjust ped models and positions, mining zones, node spawn behaviour, ore rarities, smelting recipes, selling prices, upgrade scaling, durability loss and more.

5. **(Optional) Enable ox_target** – set `Config.UseOxTarget = true` to use the built‑in `ox_target` integration.  If disabled the fallback `qb-target` will be used.  Ensure the corresponding resource is started before qbox‑mining.

6. **Start your server**.  Ped NPCs will spawn automatically and mining nodes will appear in the quarry.  Use a pickaxe from the shop to begin mining.

## Usage

* Speak to the **Mining Shop** NPC to buy a pickaxe, purchase upgrades, repair tools, view your stats or read the help section.
* Approach ore rocks in the quarry and press the interaction key to mine.  A progress circle and animation will play.  When complete you receive ore items and XP.  Nodes disappear and respawn later.
* Visit the **Smelting** NPC to convert ores into ingots.  Each recipe takes time; you can queue a single batch at a time.  Smelting awards a small amount of XP.
* Sell your ingots to the **Selling** NPC for cash.  Prices fluctuate slightly each time you sell.  Tax is deducted automatically.
* Level up your mining skill by earning XP.  Higher levels unlock better pickaxes and upgrades.
* Administrators can use the `/mining:setlevel`, `/mining:addxp` and `/mining:reset` commands to manage player progress.  Use `/mining:debug` to toggle on‑screen debug information.

## Customisation

The `shared/config.lua` file exposes hundreds of parameters.  Comments in that file describe the purpose of each setting.  Notable options include:

| Config Key           | Description                                                               |
|----------------------|---------------------------------------------------------------------------|
| `UseQbox`            | Toggle between Qbox (true) and QBCore (false) integration.                 |
| `UseOxTarget`        | Use ox_target when true; otherwise use qb-target.                         |
| `Peds`               | Models and coordinates for Shop, Smelt and Sell NPCs.                     |
| `Zones`              | Define mining areas via polygons or circles.                              |
| `Spawns`             | Control how many nodes spawn, their respawn times and player cooldown.    |
| `Ores`               | Define ore rarity, yield ranges, XP and level requirements.               |
| `Smelting`           | Configure smelting recipes and durations.                                |
| `SellPrices`         | Base price and variance for ingots; also specify the tax rate.            |
| `Pickaxes`           | Set pickaxe durability, speed modifiers and base prices.                  |
| `Upgrades`           | Configure upgrade caps and price scaling for speed, yield and luck.       |
| `DurabilityLoss`     | Determine how much durability each pickaxe tier loses per use.            |
| `MaxLevel`           | Set the maximum mining level players can reach.                          |
| `Contracts`          | Toggle and define daily mining contracts.                                |
| `Locale`             | Choose which localisation file to load.                                  |

## Developing Further

This resource is structured to be easy to extend.  Client logic lives in the `client/` folder and server logic in `server/`.  Shared configuration and localisation are in `shared/` and `locales/`.  The NUI (HTML, CSS, JS) lives in `web/`.  Feel free to expand the UI, add more tabs, create extra ped interactions, or integrate other resources such as in‑game economy sinks.

## Credits

Created by ChatGPT Assistant for the OpenAI Code Interpreter environment.  Uses the following FiveM libraries:

* [`ox_lib`](https://github.com/overextended/ox_lib) – UI components, polyzones and utility functions.
* [`ox_inventory`](https://github.com/overextended/ox_inventory) – inventory and item handling.
* [`ox_target`](https://github.com/overextended/ox_target) – optional target system for interactions.
* [`oxmysql`](https://github.com/overextended/oxmysql) – optional database layer for persistence.

Enjoy mining!