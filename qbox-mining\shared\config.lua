--[[
    shared/config.lua

    This file contains all configuration options for the qbox-mining resource.  Most values
    can be tuned here without changing any source files.  You can override these values
    by editing this file or supplying your own version when updating the resource.

    The config is loaded on both client and server.  Only simple Lua values (numbers,
    strings, booleans, tables) should be stored here.  Functions and exports should
    instead be placed in runtime scripts.
--]]

Config = {}

----------------------------------------------------------------------------------------------------
-- Framework Detection
--
-- Set which core framework to use.  When true the resource assumes Qbox is present; when false
-- it will fallback to QBCore.  Qbox compatibility mode calls into Qbox's player metadata and
-- account functions, while QBCore mode uses `QBCore.Functions.GetPlayer`.
Config.UseQbox = true

-- Toggle use of ox_target.  When false the fallback qb-target integration will be used.  Both
-- target systems provide the same menu options and icons.
Config.UseOxTarget = true

----------------------------------------------------------------------------------------------------
-- Ped Models and Locations
--
-- Each ped definition contains a model and a vector4 coordinate (x, y, z, heading).  Feel free
-- to replace the models with your own.  Coords should be fine tuned for your server.
Config.Peds = {
    Shop = {
        model = 's_m_y_construct_01',
        coords = vector4(2944.0234, 2748.1826, 43.3554, 281.0099)
    },
    Sell = {
        model = 's_m_m_dockwork_01',
        coords = vector4(1090.0004, -1999.4681, 30.9296, 153.6010)
    },
    Smelt = {
        model = 's_m_m_scientist_01',
        coords = vector4(1109.6466, -2008.5331, 31.0583, 246.0509)
    }
}

----------------------------------------------------------------------------------------------------
-- Zone Definitions
--
-- Mining zones can either be polygonal (list of points) or circular (centre with radius).  The
-- primary mining location is the Davis Quartz Quarry.  Additional zones can be added to the
-- `Zones` table.  Each zone must have a unique name and either a `points` table (poly) or
-- `coords` and `radius` (circle).
Config.Zones = {
    Quarry = {
        type = 'poly',
        points = {
            vec2(2960.6, 2765.5),
            vec2(2927.7, 2783.9),
            vec2(2910.4, 2772.9),
            vec2(2902.5, 2739.1),
            vec2(2916.9, 2711.6),
            vec2(2935.5, 2704.9),
            vec2(2956.8, 2710.1),
            vec2(2972.3, 2727.6),
            vec2(2975.1, 2748.5),
            vec2(2967.3, 2762.1)
        },
        thickness = 20.0 -- vertical thickness for polyzones (height span)
    }
    -- You can define additional zones here
}

----------------------------------------------------------------------------------------------------
-- Node and Spawn Settings
--
-- Spawns define how many ore nodes can appear and how quickly they respawn.  The spawnPoints
-- table holds world coordinates for potential spawn locations.  The server will randomly
-- activate nodes from this list up to `maxSpawnedNodes`.  Each node has its own cooldown
-- after being mined before it may respawn.  Additionally, players have a personal cooldown
-- to prevent spamming the mining action on different nodes.
Config.Spawns = {
    -- Reduce the number of simultaneously active nodes to prevent overcrowding and improve performance.
    -- A lower value ensures better spacing and reduces collision issues.
    maxSpawnedNodes = 75,
    -- Shorten respawn times to around one minute for faster gameplay.  Rocks will respawn
    -- randomly between 45 and 60 seconds.
    respawnTime = { min = 45000, max = 60000 }, -- 45–60 seconds
    playerCooldown = 5000, -- ms between mining attempts per player

    -- Collision detection and spacing settings
    minDistanceBetweenNodes = 8.0, -- Minimum distance between rock spawns (in game units)
    maxSpawnAttempts = 500, -- Maximum attempts to find a valid spawn position
    groundCheckRadius = 2.0, -- Radius to check for suitable ground around spawn point

    -- Distribution algorithm settings
    useGridBasedSpawning = true, -- Use grid-based distribution for better coverage
    gridCellSize = 12.0, -- Size of each grid cell (should be larger than minDistanceBetweenNodes)
    randomOffsetRange = 4.0, -- Random offset within each grid cell to avoid perfect alignment

    -- Spawn area configuration - circular area centered on the quarry
    spawnCenter = vector3(2955.7224, 2748.6758, 43.5263), -- Center of the quarry
    spawnRadius = 100.0, -- Maximum distance from center where rocks can spawn

    -- This list is kept for backwards compatibility but is no longer used for spawning.  A
    -- single entry remains to satisfy the structure.
    spawnPoints = {
        vector3(2947.2, 2748.5, 42.5)
    },
    -- New bounding area defining where rocks can spawn.  Nodes will be placed randomly
    -- within the rectangle defined by these four corner points.
    spawnBounds = {
        vector4(3045.3484, 2850.4456, 81.2166, 116.1784),
        vector4(2946.4429, 2661.7141, 75.0598, 351.3775),
        vector4(2844.9353, 2829.4541, 52.2261, 257.9532),
        vector4(2939.6750, 2919.1897, 88.8655, 152.8939)
    }
}

----------------------------------------------------------------------------------------------------
-- Ore Definitions
--
-- Each entry describes an ore type that can spawn in nodes.  `rarity` is the chance
-- (out of 100) that this ore will spawn relative to others when a node is created.  A higher
-- rarity number means the ore is more common.  `yield` defines the range of how many items
-- are awarded per successful mining attempt.  `xp` is the XP gained per node.
Config.Ores = {
    iron = {
        label = 'Iron Ore',
        item = 'ore_iron',
        rarity = 50,
        yield = { min = 1, max = 3 },
        xp = 10,
        levelMin = 1,
        -- Use a large rock model to make nodes more visible.  This prop is considerably
        -- bigger than the defaults and better fills the quarry.  Feel free to swap
        -- for other rock props if desired.
        model = 'prop_rock_1_f'
    },
    copper = {
        label = 'Copper Ore',
        item = 'ore_copper',
        rarity = 25,
        yield = { min = 1, max = 3 },
        xp = 12,
        levelMin = 5,
        model = 'prop_rock_1_g'
    },
    aluminum = {
        label = 'Aluminum Ore',
        item = 'ore_aluminum',
        rarity = 15,
        yield = { min = 1, max = 2 },
        xp = 16,
        levelMin = 10,
        model = 'prop_rock_2_g'
    },
    gold = {
        label = 'Gold Ore',
        item = 'ore_gold',
        rarity = 8,
        yield = { min = 1, max = 2 },
        xp = 20,
        levelMin = 15,
        model = 'prop_rock_5_f'
    },
    titanium = {
        label = 'Titanium Ore',
        item = 'ore_titanium',
        rarity = 2,
        yield = { min = 1, max = 1 },
        xp = 30,
        levelMin = 25,
        model = 'prop_rock_5_e'
    }
}

----------------------------------------------------------------------------------------------------
-- Rare Drop Table
--
-- When mining there is a chance to find a rare drop.  `chance` is the base 1–100 roll.
-- Upgrades can improve this chance on the server side.  The `drops` list defines what items
-- may drop; if the roll succeeds one of these will be selected randomly.  Additional rewards
-- can be added or removed.
Config.RareDrops = {
    chance = 5, -- base % chance to drop any rare item
    drops = {
        { item = 'gem_emerald', min = 1, max = 1 },
        { item = 'gem_ruby',    min = 1, max = 1 },
        { item = 'gem_sapphire',min = 1, max = 1 },
        { item = 'geode',       min = 1, max = 1 }
    }
}

----------------------------------------------------------------------------------------------------
-- Smelting Recipes
--
-- Defines how raw ores convert into ingots.  Each recipe requires a number of input
-- items (inputQty) and takes a given time in milliseconds to complete.  XP is awarded
-- for completing a smelt.  Additional recipes can be added as needed.
Config.Smelting = {
    { input = 'ore_iron',     output = 'ingot_iron',     inputQty = 2, time = 8000, xp = 4 },
    { input = 'ore_copper',   output = 'ingot_copper',   inputQty = 2, time = 8500, xp = 4 },
    { input = 'ore_aluminum', output = 'ingot_aluminum', inputQty = 2, time = 9000, xp = 5 },
    { input = 'ore_gold',     output = 'ingot_gold',     inputQty = 2, time = 12000, xp = 6 },
    { input = 'ore_titanium', output = 'ingot_titanium', inputQty = 2, time = 15000, xp = 8 }
}

----------------------------------------------------------------------------------------------------
-- Selling Prices
--
-- Each ingot has a base price.  A variance percentage applies a random fluctuation around
-- the base price each time the server calculates totals.  Additional dynamic modifiers
-- (time of day, event multipliers) should be applied server side.  Tax is a flat percent
-- taken from the final payout.
Config.SellPrices = {
    { item = 'ingot_iron',     base = 80,  variance = 0.10 },
    { item = 'ingot_copper',   base = 100, variance = 0.10 },
    { item = 'ingot_aluminum', base = 120, variance = 0.12 },
    { item = 'ingot_gold',     base = 450, variance = 0.15 },
    { item = 'ingot_titanium', base = 600, variance = 0.15 }
}
Config.TaxRate = 0.05 -- 5% tax applied on sale

----------------------------------------------------------------------------------------------------
-- Pickaxe Definitions
--
-- Pickaxes are items with durability and optional upgrades.  Upgrades are stored in
-- metadata.  `durability` determines how many uses before the tool breaks.  `speed`
-- modifies the mining duration on the client (lower = faster).  Additional metadata
-- fields (yield, luck) are applied server side during reward calculations.
Config.Pickaxes = {
    pickaxe_basic = {
        label = 'Basic Pickaxe',
        durability = 100,
        speed = 1.0,
        -- Set price to zero for testing so players can obtain pickaxes without
        -- requiring currency.  Remove or adjust these values when ready to
        -- enable purchases.
        price = 0
    },
    pickaxe_sturdy = {
        label = 'Sturdy Pickaxe',
        durability = 200,
        speed = 0.9,
        price = 0
    },
    pickaxe_industrial = {
        label = 'Industrial Pickaxe',
        durability = 400,
        speed = 0.8,
        price = 0
    }
}

----------------------------------------------------------------------------------------------------
-- Upgrade Settings
--
-- Upgrades allow players to improve their pickaxes.  Each upgrade has a maximum level and
-- price scaling.  Prices scale exponentially based on the current upgrade level.  The
-- upgrade effects (speed, yield, luck) are applied in various parts of the system.
Config.Upgrades = {
    speed =    { max = 5, priceBase = 1000, priceScale = 1.35 },
    yield =    { max = 5, priceBase = 1200, priceScale = 1.35 },
    luck  =    { max = 5, priceBase = 1500, priceScale = 1.35 }
}

----------------------------------------------------------------------------------------------------
-- Durability Loss per Pickaxe Tier
--
-- When mining a node, the pickaxe loses durability according to its tier.  Lower numbers
-- represent longer lasting tools.  Once durability reaches 0 the pickaxe becomes broken
-- and must be repaired or replaced.
Config.DurabilityLoss = {
    pickaxe_basic = 2,
    pickaxe_sturdy = 1,
    pickaxe_industrial = 0.5
}

----------------------------------------------------------------------------------------------------
-- Leveling & Experience
--
-- Players have a single mining level which increases their efficiency.  XP required for
-- the next level follows the formula below.  Feel free to adjust the constants to
-- achieve your desired progression curve.  The `levelXp` function returns the
-- required XP for a given level.  MaxLevel caps how far players can progress.
Config.MaxLevel = 100
Config.levelXp = function(level)
    return math.floor(50 + (level ^ 1.35) * 12)
end

----------------------------------------------------------------------------------------------------
-- Contracts and Extras
--
-- Daily contracts and other bonuses are optional features.  Set enable to true to
-- allow these systems.  The contract table defines a set of tasks players can
-- complete for bonus rewards.  When false the system is disabled entirely.
Config.Contracts = {
    enable = false,
    tasks = {
        { description = 'Smelt and deliver 20 Copper Ingots', item = 'ingot_copper', amount = 20, reward = 5000, xp = 50 },
        { description = 'Deliver 10 Gold Ingots', item = 'ingot_gold', amount = 10, reward = 8000, xp = 80 }
    }
}

----------------------------------------------------------------------------------------------------
-- Locale Selection
--
-- Specify which language file to load from the locales/ directory.  When adding new
-- languages create a file with the appropriate translations and set Locale accordingly.
Config.Locale = 'en'

-- End of configuration