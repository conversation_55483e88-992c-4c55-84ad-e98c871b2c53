// app.js
//
// NUI logic for the mining system.  Listens for messages from the Lua side and
// renders different tabs for the shop, smelting and selling interfaces.  Uses
// simple fetch POST requests to trigger server callbacks via NUI callbacks.

const appEl = document.getElementById('mining-app');
const contentEl = document.getElementById('content');
const profileEl = document.getElementById('profile-info');
const closeBtn = document.getElementById('close-btn');
const tabButtons = document.querySelectorAll('.tab-btn');

// Keep track of which tabs are allowed.  When defined, the UI will hide
// buttons and prevent switching to tabs not listed.  This is provided
// from the Lua side when opening the UI.
let allowedTabsList = null;

let profile = null;
let config = null;
let currentTab = 'shop';

// Display a small toast notification
function notify(msg) {
    const div = document.createElement('div');
    div.className = 'toast';
    div.innerText = msg;
    document.body.appendChild(div);
    setTimeout(() => {
        div.style.opacity = '0';
        setTimeout(() => div.remove(), 500);
    }, 2500);
}

// Listen for messages from Lua
window.addEventListener('message', (event) => {
    const data = event.data;
    if (data.action === 'open') {
        profile = data.profile || profile;
        config = data.config || config;
        currentTab = data.tab || 'shop';
        allowedTabsList = Array.isArray(data.allowedTabs) ? data.allowedTabs : null;
        appEl.classList.remove('hidden');
        updateProfile();
        updateNavVisibility();
        showTab(currentTab);
    } else if (data.action === 'close') {
        appEl.classList.add('hidden');
        // Reset allowedTabs when closed so UI shows all tabs next time by default
        allowedTabsList = null;
    } else if (data.action === 'profile') {
        profile = data.profile;
        updateProfile();
    }
});

// Close button behaviour
closeBtn.addEventListener('click', () => {
    fetch(`https://${GetParentResourceName()}/close`, { method: 'POST', body: '{}' });
});

// Tab navigation
tabButtons.forEach(btn => {
    btn.addEventListener('click', () => {
        const tab = btn.getAttribute('data-tab');
        // Prevent switching to a tab that isn't allowed
        if (allowedTabsList && !allowedTabsList.includes(tab)) return;
        showTab(tab);
    });
});

// Show or hide navigation buttons depending on allowedTabsList
function updateNavVisibility() {
    tabButtons.forEach(btn => {
        const tab = btn.getAttribute('data-tab');
        if (allowedTabsList && !allowedTabsList.includes(tab)) {
            btn.style.display = 'none';
        } else {
            btn.style.display = 'block';
        }
    });
}

function showTab(tab) {
    currentTab = tab;
    tabButtons.forEach(btn => btn.classList.toggle('active', btn.getAttribute('data-tab') === tab));
    if (!config) return;
    if (tab === 'shop') renderShop();
    else if (tab === 'smelt') renderSmelt();
    else if (tab === 'sell') renderSell();
}

function updateProfile() {
    if (!profile) return;
    profileEl.innerHTML = `Lvl ${profile.level} | XP ${profile.xp}`;
}

// Render the pickaxe shop
function renderShop() {
    contentEl.innerHTML = '';
    const title = document.createElement('h2');
    title.innerText = 'Pickaxes';
    contentEl.appendChild(title);
    const pickaxes = config.pickaxes;
    for (const name in pickaxes) {
        const def = pickaxes[name];
        const card = document.createElement('div');
        card.className = 'card';
        const info = document.createElement('div');
        info.className = 'info';
        info.innerHTML = `<strong>${def.label}</strong><br>Durability: ${def.durability}<br>Speed: ${def.speed}<br>Price: $${def.price}`;
        const btn = document.createElement('button');
        btn.innerText = 'Buy';
        btn.addEventListener('click', async () => {
            const res = await post('purchasePickaxe', { pickaxe: name });
            if (res.success) notify('Purchased successfully');
            else notify(res.message || 'Purchase failed');
        });
        card.appendChild(info);
        card.appendChild(btn);
        contentEl.appendChild(card);
    }
}

// Render the smelting interface
function renderSmelt() {
    contentEl.innerHTML = '';
    const title = document.createElement('h2');
    title.innerText = 'Smelting';
    contentEl.appendChild(title);
    const recipes = config.smelting;
    recipes.forEach((recipe, index) => {
        const card = document.createElement('div');
        card.className = 'card';
        const info = document.createElement('div');
        info.className = 'info';
        info.innerHTML = `<strong>${recipe.input} → ${recipe.output}</strong><br>Requires: ${recipe.inputQty}x ${recipe.input}<br>Time: ${recipe.time/1000}s`;
        const input = document.createElement('input');
        input.type = 'number';
        input.min = 1;
        input.value = 1;
        const btn = document.createElement('button');
        btn.innerText = 'Smelt';
        btn.addEventListener('click', async () => {
            const qty = parseInt(input.value) || 1;
            const res = await post('smelt', { recipeIndex: index + 1, quantity: qty });
            if (res.success) notify(res.message);
            else notify(res.message || 'Smelt failed');
        });
        card.appendChild(info);
        card.appendChild(input);
        card.appendChild(btn);
        contentEl.appendChild(card);
    });
}

// Render the selling interface
function renderSell() {
    contentEl.innerHTML = '';
    const title = document.createElement('h2');
    title.innerText = 'Sell Ingots';
    contentEl.appendChild(title);
    const desc = document.createElement('p');
    desc.innerText = 'Click the button below to sell all your ingots.';
    contentEl.appendChild(desc);
    const btn = document.createElement('button');
    btn.innerText = 'Sell All';
    btn.addEventListener('click', async () => {
        // Build item list from config
        const items = [];
        config.sellPrices.forEach(p => {
            items.push({ item: p.item, qty: -1 }); // -1 tells the server to sell all of that item
        });
        const res = await post('sellItems', { items: items });
        notify(res.message || (res.success ? 'Sold items' : 'Sell failed'));
    });
    contentEl.appendChild(btn);
}

// Helper to post to NUI callbacks
async function post(event, data) {
    const resource = GetParentResourceName();
    const response = await fetch(`https://${resource}/${event}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data || {})
    });
    try {
        return await response.json();
    } catch (e) {
        return {};
    }
}