--[[
    client/cl_target.lua

    Registers interactive targets for NPCs.  Supports ox_target if enabled via
    configuration, otherwise falls back to qb-target.  Each ped has a category with
    separate options for Shop, Smelt and Sell interactions.
--]]

local function addTarget(ped, options)
    if Config.UseOxTarget and GetResourceState('ox_target') ~= 'missing' then
        exports.ox_target:addLocalEntity(ped, options)
    elseif GetResourceState('qb-target') ~= 'missing' then
        exports['qb-target']:AddTargetEntity(ped, {
            options = options,
            distance = 2.0
        })
    else
        print('qbox-mining: No target system available!')
    end
end

-- Register target options for each ped
function RegisterPedTargets()
    -- Wait until peds have been spawned
    Wait(0)
    local peds = exports['qbox-mining']:getPeds() or {}
    for name, ped in pairs(peds) do
        local options = {}
        if name == 'Shop' then
            table.insert(options, {
                name = 'mining_shop',
                label = L('shop_ped_label'),
                icon = 'fa-solid fa-hammer',
                onSelect = function()
                    -- Only allow access to the shop tab when interacting with the shop ped
                    TriggerEvent('qbox-mining:client:openUi', { tab = 'shop', extra = { allowedTabs = { 'shop' } } })
                end
            })
        elseif name == 'Smelt' then
            table.insert(options, {
                name = 'mining_smelt',
                label = L('smelt_ped_label'),
                icon = 'fa-solid fa-fire',
                onSelect = function()
                    -- Only allow access to the smelting tab when interacting with the smelter ped
                    TriggerEvent('qbox-mining:client:openUi', { tab = 'smelt', extra = { allowedTabs = { 'smelt' } } })
                end
            })
        elseif name == 'Sell' then
            table.insert(options, {
                name = 'mining_sell',
                label = L('sell_ped_label'),
                icon = 'fa-solid fa-coins',
                onSelect = function()
                    -- Only allow access to the sell tab when interacting with the selling ped
                    TriggerEvent('qbox-mining:client:openUi', { tab = 'sell', extra = { allowedTabs = { 'sell' } } })
                end
            })
        end
        if #options > 0 then
            addTarget(ped, options)
        end
    end
end

-- We need a way to access spawned peds from this module; expose via exports
-- The ped table is maintained in cl_peds.lua and assigned to _G for exports
function getPeds()
    return _G['spawnedPeds'] or {}
end

exports('RegisterPedTargets', RegisterPedTargets)
exports('getPeds', getPeds)