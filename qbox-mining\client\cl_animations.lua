--[[
    client/cl_animations.lua

    Handles the animations and props used when mining.  Pickaxes are spawned and attached
    to the player's hand during the action.  After the progress completes or is cancelled
    the prop is removed.  All dictionary loading is done here to avoid duplication.
--]]

-- Use a more appropriate mining animation.  This dictionary depicts a
-- construction worker using a drill/pick.  Feel free to change the
-- dictionary and animation name to suit your server.
local miningAnimDict = 'amb@world_human_const_drill@male@base'
local miningAnimName = 'base'
local pickaxeModel = 'prop_tool_pickaxe'

local function loadAnimDict(dict)
    if HasAnimDictLoaded(dict) then return end
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
    end
end

-- Spawns and attaches the pickaxe prop to the player's hand
local function attachPickaxe()
    local ped = PlayerPedId()
    local model = GetHashKey(pickaxeModel)
    RequestModel(model)
    while not HasModelLoaded(model) do Wait(10) end
    local obj = CreateObject(model, 0.0, 0.0, 0.0, true, true, false)
    local bone = GetPedBoneIndex(ped, 57005) -- right hand
    AttachEntityToEntity(obj, ped, bone, 0.10, 0.02, -0.02, -40.0, 0.0, 90.0, true, true, false, true, 1, true)
    return obj
end

-- Detaches and deletes the pickaxe prop
local function detachPickaxe(obj)
    if obj and DoesEntityExist(obj) then
        DetachEntity(obj, true, false)
        DeleteEntity(obj)
    end
end

-- Plays the mining animation and progress bar.  Duration is scaled by the pickaxe speed
-- modifier.  Returns true if completed successfully, false if cancelled.
function PlayMiningAction(duration, speedModifier)
    local ped = PlayerPedId()
    -- Load animation and attach prop
    loadAnimDict(miningAnimDict)
    local pickaxeObj = attachPickaxe()
    -- Play animation loop
    TaskPlayAnim(ped, miningAnimDict, miningAnimName, 1.0, 1.0, -1, 49, 0, false, false, false)
    -- Calculate adjusted duration
    local adjusted = duration * (speedModifier or 1.0)
    -- Show progress circle
    local finished = lib.progressCircle({
        duration = adjusted,
        label = L('mining_progress', ''),
        position = 'bottom',
        useWhileDead = false,
        canCancel = true,
        disable = {
            move = true,
            car = true,
            mouse = false,
            combat = true
        }
    })
    -- Clear animation
    ClearPedTasks(ped)
    detachPickaxe(pickaxeObj)
    return finished -- boolean: true if completed, false if cancelled
end

exports('PlayMiningAction', PlayMiningAction)